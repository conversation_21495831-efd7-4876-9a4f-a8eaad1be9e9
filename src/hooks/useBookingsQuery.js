import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { toast } from 'react-hot-toast';
import { parseISO, differenceInHours } from 'date-fns';

const BOOKINGS_KEY = 'bookings';

export const useBookings = (userId) => {
  return useQuery({
    queryKey: [BOOKINGS_KEY, userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          session:sessions (
            id,
            title,
            type,
            description,
            start_time,
            end_time,
            max_participants,
            participant_count,
            coach_id,
            school
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Debug: Log user bookings
      console.log(`useBookings: User ${userId} has ${data?.length || 0} bookings`);
      console.log('useBookings: User bookings data:', data);

      return data;
    },
    enabled: !!userId
  });
};

export const useBooking = (bookingId) => {
  return useQuery({
    queryKey: [BOOKINGS_KEY, bookingId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          session:sessions (
            id,
            title,
            type,
            description,
            start_time,
            end_time,
            max_participants,
            participant_count,
            coach_id,
            school
          )
        `)
        .eq('id', bookingId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!bookingId
  });
};

export const useCreateBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ sessionId, userId }) => {
      // First check if session exists and has space
      const { data: session, error: sessionError } = await supabase
        .from('sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      if (session.participant_count >= session.max_participants) {
        throw new Error('Session is full');
      }

      // Check if user already has a booking for this session
      const { data: existingBooking, error: bookingError } = await supabase
        .from('bookings')
        .select('*')
        .eq('session_id', sessionId)
        .eq('user_id', userId)
        .neq('status', 'cancelled')
        .maybeSingle();

      if (bookingError) throw bookingError;
      if (existingBooking) throw new Error('You already have a booking for this session');

      // First get the latest payment (either loyalty or once-off)
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .select('*')
        .eq('user_id', userId)
        .in('plan_id', ['loyalty-card', 'once-off'])
        .eq('status', 'completed')
        .order('payment_date', { ascending: false })
        .limit(1)
        .single();

      if (paymentError && paymentError.code !== 'PGRST116') { // Ignore "no rows returned" error
        console.error('Error getting payment:', paymentError);
        throw paymentError;
      }

      let loyaltySessionId = null;
      let onceOffSessionId = null;

      if (payment) {
        if (payment.plan_id === 'loyalty-card') {
          // Check loyalty card status
          const { data: cardStatus, error: cardError } = await supabase
            .rpc('get_loyalty_card_status', { p_user_id: userId });

          if (cardError) {
            console.error('Error checking loyalty card status:', cardError);
            throw cardError;
          }

          if (!cardStatus?.[0]?.has_active_card) {
            throw new Error('You have used all your loyalty card sessions. Please purchase a new loyalty card to continue booking.');
          }

          // Find an available loyalty session for this payment
          const { data: loyaltyData, error: loyaltyError } = await supabase
            .from('loyalty_sessions')
            .select('*')
            .eq('user_id', userId)
            .eq('payment_id', payment.id)
            .eq('status', 'pending')
            .order('created_at', { ascending: true })
            .limit(1)
            .single();

          if (loyaltyError && loyaltyError.code !== 'PGRST116') {
            console.error('Error checking loyalty session:', loyaltyError);
          } else if (loyaltyData) {
            // Update the loyalty session to used
            const { error: updateError } = await supabase
              .from('loyalty_sessions')
              .update({
                status: 'used',
                used_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', loyaltyData.id)
              .select()
              .single();

            if (updateError) {
              console.error('Error updating loyalty session:', updateError);
            } else {
              loyaltySessionId = loyaltyData.id;
            }
          } else {
            // Create a new loyalty session if none exists
            const { data: newSession, error: createError } = await supabase
              .from('loyalty_sessions')
              .insert({
                user_id: userId,
                payment_id: payment.id,
                status: 'used',
                used_at: new Date().toISOString()
              })
              .select()
              .single();

            if (createError) {
              console.error('Error creating loyalty session:', createError);
            } else {
              loyaltySessionId = newSession.id;
            }
          }
        } else if (payment.plan_id === 'once-off') {
          // Check once-off session status
          const { data: sessionStatus, error: statusError } = await supabase
            .rpc('get_once_off_session_status', { p_user_id: userId });

          if (statusError) {
            console.error('Error checking once-off session status:', statusError);
            throw statusError;
          }

          if (!sessionStatus?.[0]?.has_session) {
            throw new Error('You have no available once-off sessions. Please purchase a new session to continue booking.');
          }

          // Create or update once-off session
          if (sessionStatus[0].session_id) {
            // Update existing session
            const { error: updateError } = await supabase
              .from('once_off_sessions')
              .update({
                status: 'used',
                used_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', sessionStatus[0].session_id)
              .select()
              .single();

            if (updateError) {
              console.error('Error updating once-off session:', updateError);
              throw updateError;
            }
            onceOffSessionId = sessionStatus[0].session_id;
          } else {
            // Create new session
            const { data: newSession, error: createError } = await supabase
              .from('once_off_sessions')
              .insert({
                user_id: userId,
                payment_id: payment.id,
                status: 'used',
                used_at: new Date().toISOString()
              })
              .select()
              .single();

            if (createError) {
              console.error('Error creating once-off session:', createError);
              throw createError;
            }
            onceOffSessionId = newSession.id;
          }
        }
      }

      // Create booking
      const { data: booking, error: createError } = await supabase
        .from('bookings')
        .insert({
          session_id: sessionId,
          user_id: userId,
          status: 'confirmed',
          used_loyalty_session: !!loyaltySessionId,
          used_once_off_session: !!onceOffSessionId
        })
        .select()
        .single();

      if (createError) throw createError;

      // Update session IDs if used
      if (loyaltySessionId) {
        const { error: updateError } = await supabase
          .from('loyalty_sessions')
          .update({
            booking_id: booking.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', loyaltySessionId);

        if (updateError) {
          console.error('Error updating loyalty session booking ID:', updateError);
        }

        // Get updated loyalty card status
        const { data: cardStatus, error: cardError } = await supabase
          .rpc('get_loyalty_card_status', { p_user_id: userId });

        if (cardError) {
          console.error('Error getting loyalty card status:', cardError);
        } else if (cardStatus && cardStatus.length > 0) {
          booking.sessionsRemaining = cardStatus[0].sessions_remaining;
        }
      } else if (onceOffSessionId) {
        const { error: updateError } = await supabase
          .from('once_off_sessions')
          .update({
            booking_id: booking.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', onceOffSessionId);

        if (updateError) {
          console.error('Error updating once-off session booking ID:', updateError);
        }
      }

      // We no longer need to update the participant_count field
      // as we're now calculating it dynamically from the bookings

      return booking;
    },
    onSuccess: (booking) => {
      if (booking.used_loyalty_session) {
        toast.success(`Session booked successfully! ${booking.sessionsRemaining} sessions remaining.`);
      } else if (booking.used_once_off_session) {
        toast.success('Session booked successfully! Your once-off session has been used.');
      } else {
        toast.success('Session booked successfully!');
      }
      // Invalidate all relevant queries with refetchType: 'all' to ensure immediate updates
      queryClient.invalidateQueries({
        queryKey: [BOOKINGS_KEY],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['sessions'],
        refetchType: 'all',
        exact: false
      });
      queryClient.invalidateQueries({
        queryKey: ['loyalty_sessions', booking.user_id],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['loyalty_card_status', booking.user_id],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['membership', booking.user_id],
        refetchType: 'all'
      });

      // Force an immediate refetch of all sessions data
      queryClient.refetchQueries({
        queryKey: ['sessions'],
        exact: false
      });
    }
  });
};

export const useCancelBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (bookingId) => {
      if (!bookingId) {
        throw new Error('Booking ID is required');
      }

      // Get booking data with session information
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select(`
          *,
          session:sessions (
            id,
            start_time,
            title
          )
        `)
        .eq('id', bookingId)
        .single();

      if (bookingError) throw bookingError;

      if (!booking) {
        throw new Error(`Booking with ID ${bookingId} not found`);
      }

      // Check if cancellation is within 1 hour of session start
      if (booking.session && booking.session.start_time) {
        const sessionStartTime = parseISO(booking.session.start_time);
        const now = new Date();
        const hoursUntilSession = differenceInHours(sessionStartTime, now);

        if (hoursUntilSession < 1) {
          throw new Error('Cannot cancel booking less than 1 hour before the session starts');
        }
      }

      // Return loyalty session if it was used
      if (booking.used_loyalty_session) {
        // Find the loyalty session used for this booking
        const { data: loyaltySession, error: loyaltyError } = await supabase
          .from('loyalty_sessions')
          .select('*')
          .eq('booking_id', bookingId)
          .single();

        if (loyaltyError) {
          console.error('Error finding loyalty session:', loyaltyError);
          throw loyaltyError;
        }

        if (!loyaltySession) {
          throw new Error('No loyalty session found for this booking');
        }

        // Update the loyalty session to pending
        const { error: updateError } = await supabase
          .from('loyalty_sessions')
          .update({
            status: 'pending',
            booking_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', loyaltySession.id);

        if (updateError) {
          console.error('Error updating loyalty session:', updateError);
          throw updateError;
        }

        console.log('Loyalty session returned successfully');
      } else if (booking.used_once_off_session) {
        // Find the once-off session used for this booking
        const { data: onceOffSession, error: sessionError } = await supabase
          .from('once_off_sessions')
          .select('*')
          .eq('booking_id', bookingId)
          .single();

        if (sessionError) {
          console.error('Error finding once-off session:', sessionError);
          throw sessionError;
        }

        if (!onceOffSession) {
          throw new Error('No once-off session found for this booking');
        }

        // Update the once-off session to pending
        const { error: updateError } = await supabase
          .from('once_off_sessions')
          .update({
            status: 'pending',
            booking_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', onceOffSession.id);

        if (updateError) {
          console.error('Error updating once-off session:', updateError);
          throw updateError;
        }

        console.log('Once-off session returned successfully');
      }

      // Update booking status to cancelled
      const { error: updateBookingError } = await supabase
        .from('bookings')
        .update({ status: 'cancelled' })
        .eq('id', bookingId);

      if (updateBookingError) throw updateBookingError;

      // We no longer need to update the participant_count field
      // as we're now calculating it dynamically from the bookings

      return booking;
    },
    onMutate: (bookingId) => {
      // Store the booking ID being cancelled to use in the UI
      return { bookingId };
    },
    onSuccess: (booking, bookingId, context) => {
      // Get the user ID from the booking
      const userId = booking?.user_id;

      // Invalidate all relevant queries with refetchType: 'all' to ensure immediate updates
      queryClient.invalidateQueries({
        queryKey: [BOOKINGS_KEY],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['sessions'],
        refetchType: 'all',
        exact: false
      });

      // Only invalidate user-specific queries if we have a user ID
      if (userId) {
        queryClient.invalidateQueries({
          queryKey: ['loyalty_sessions', userId],
          refetchType: 'all'
        });
        queryClient.invalidateQueries({
          queryKey: ['loyalty_card_status', userId],
          refetchType: 'all'
        });
        queryClient.invalidateQueries({
          queryKey: ['membership', userId],
          refetchType: 'all'
        });
      }

      // Force an immediate refetch of all sessions data
      queryClient.refetchQueries({
        queryKey: ['sessions'],
        exact: false
      });

      // Show success message
      toast.success('Booking cancelled successfully');
    }
  });
};