import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

const MEMBERSHIPS_KEY = ['memberships'];

export const useMemberships = () => {
  return useQuery({
    queryKey: MEMBERSHIPS_KEY,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memberships')
        .select(`
          *,
          users!user_id (
            id
          ),
          profiles!users!user_id!id (
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    }
  });
};

export const useActiveMemberships = () => {
  return useQuery({
    queryKey: [...MEMBERSHIPS_KEY, 'active'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memberships')
        .select(`
          *,
          users!user_id (
            id
          ),
          profiles!users!user_id!id (
            first_name,
            last_name,
            email
          )
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    }
  });
};

export const useMembership = (id) => {
  return useQuery({
    queryKey: [...MEMBERSHIPS_KEY, id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memberships')
        .select(`
          *,
          users!user_id (
            id
          ),
          profiles!users!user_id!id (
            first_name,
            last_name,
            email
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id
  });
};

export const useUserMemberships = (userId) => {
  return useQuery({
    queryKey: [...MEMBERSHIPS_KEY, 'user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memberships')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId
  });
};

export const useCreateMembership = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (membershipData) => {
      const { data, error } = await supabase
        .from('memberships')
        .insert([membershipData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: MEMBERSHIPS_KEY });
      queryClient.invalidateQueries({ queryKey: [...MEMBERSHIPS_KEY, 'user', data.user_id] });
    }
  });
};

export const useUpdateMembership = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }) => {
      const { data, error } = await supabase
        .from('memberships')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: MEMBERSHIPS_KEY });
      queryClient.invalidateQueries({ queryKey: [...MEMBERSHIPS_KEY, data.id] });
      queryClient.invalidateQueries({ queryKey: [...MEMBERSHIPS_KEY, 'user', data.user_id] });
    }
  });
};