import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '../lib/supabase';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  differenceInDays,
  differenceInMonths,
  addMonths,
  differenceInHours
} from 'date-fns';

// Define a query key for membership data
const MEMBERSHIP_KEY = 'membership';

export const useMembership = (userId) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const effectiveUserId = userId || user?.id;

  const calculateTimeRemaining = (endDate) => {
    if (!endDate) return 'No active membership';

    const now = new Date();
    const end = new Date(endDate);

    if (end < now) return 'Expired';

    // Handle day membership
    const hours = differenceInHours(end, now);
    if (hours < 24) {
      return `${hours}h remaining`;
    }

    const months = differenceInMonths(end, now);

    if (months > 0) {
      // Calculate the date after adding the full months
      const afterMonths = addMonths(now, months);
      // Get the actual remaining days
      const remainingDays = differenceInDays(end, afterMonths);

      if (remainingDays === 0) {
        return months === 1 ? '1 month remaining' : `${months} months remaining`;
      }
      return months === 1
        ? `1 month and ${remainingDays} days remaining`
        : `${months} months and ${remainingDays} days remaining`;
    }

    const days = differenceInDays(end, now);
    const weeks = Math.floor(days / 7);

    if (weeks > 0) {
      const remainingDays = days % 7;
      if (remainingDays === 0) {
        return weeks === 1 ? '1 week remaining' : `${weeks} weeks remaining`;
      }
      return weeks === 1
        ? `1 week and ${remainingDays} days remaining`
        : `${weeks} weeks and ${remainingDays} days remaining`;
    }

    if (days === 0) return 'Last day';
    return days === 1 ? '1 day remaining' : `${days} days remaining`;
  };

  // Use React Query to fetch and cache membership data
  const { data: membershipDetails, isLoading, refetch } = useQuery({
    queryKey: [MEMBERSHIP_KEY, effectiveUserId],
    queryFn: async () => {
      if (!effectiveUserId) {
        return {
          membership_status: 'inactive',
          membership_type: null,
          endDate: null,
          isExpired: true,
          latestPayment: null,
          timeRemaining: 'No active membership',
          sessionsUsed: 0,
          sessionsRemaining: 0,
          sessions: []
        };
      }

      try {
        // Get both profile and latest payment data
        const [profileResult, paymentResult] = await Promise.all([
          supabase
            .from('profiles')
            .select('membership_status, membership_type, membership_start_date, membership_end_date')
            .eq('id', effectiveUserId)
            .maybeSingle(),
          supabase
            .from('payments')
            .select('*')
            .eq('user_id', effectiveUserId)
            .order('payment_date', { ascending: false })
            .limit(1)
            .maybeSingle()
        ]);

        if (profileResult.error) throw profileResult.error;

        // Use profile data as source of truth for membership status
        const profile = profileResult.data;
        const payment = paymentResult.data; // May be null if no payments exist

        if (!profile) {
          return {
            membership_status: 'inactive',
            membership_type: null,
            endDate: null,
            isExpired: true,
            latestPayment: payment,
            timeRemaining: 'No active membership',
            sessionsUsed: 0,
            sessionsRemaining: 0,
            sessions: []
          };
        }

        const now = new Date();
        const endDate = profile.membership_end_date ? new Date(profile.membership_end_date) : null;

        // Special handling for loyalty cards and once-off sessions - they expire based on sessions used, not date
        const isLoyaltyCard = payment?.plan_id === 'loyalty-card' || profile.membership_type === 'loyalty-card';
        const isOnceOff = payment?.plan_id === 'once-off' || profile.membership_type === 'once-off';

        let isExpired = !isLoyaltyCard && !isOnceOff && (endDate ? endDate < now : true);
        let sessionsUsed = 0;
        let sessionsRemaining = 0;
        let sessions = [];

        // For loyalty cards, use get_loyalty_card_status to determine expiry
        if (isLoyaltyCard) {
          try {
            const { data: cardStatusArray, error: cardError } = await supabase
              .rpc('get_loyalty_card_status', {
                p_user_id: effectiveUserId
              });

            if (cardError) {
              isExpired = true;
            } else if (cardStatusArray && cardStatusArray.length > 0) {
              const cardStatus = cardStatusArray[0]; // Get first item from array
              // For loyalty cards, isExpired is the opposite of has_active_card
              isExpired = !cardStatus.has_active_card;
              sessionsUsed = cardStatus.sessions_used || 0;
              sessionsRemaining = cardStatus.sessions_remaining || 0;

              // Only fetch sessions for display purposes, don't use for counting
              if (!isExpired) {
                try {
                  const { data: loyaltySessions, error: sessionsError } = await supabase
                    .from('loyalty_sessions')
                    .select('*')
                    .eq('user_id', effectiveUserId)
                    .order('used_at', { ascending: false });

                  if (!sessionsError) {
                    sessions = loyaltySessions || [];
                  }
                } catch (error) {
                  console.error('Error fetching loyalty sessions:', error);
                }
              }
            } else {
              console.error('No card status returned');
              isExpired = true;
            }
          } catch (error) {
            console.error('Error checking loyalty card status:', error);
            isExpired = true;
          }
        } else if (isOnceOff) {
          try {
            const { data: sessionStatus, error: statusError } = await supabase
              .rpc('get_once_off_session_status', {
                p_user_id: effectiveUserId
              });

            if (statusError) {
              console.error('Error checking once-off session status:', statusError);
              isExpired = true;
            } else if (sessionStatus && sessionStatus.length > 0) {
              const status = sessionStatus[0];
              // For once-off sessions, isExpired is the opposite of has_session
              isExpired = !status.has_session;
              sessionsUsed = isExpired ? 1 : 0;
              sessionsRemaining = isExpired ? 0 : 1;
            } else {
              isExpired = true;
            }
          } catch (error) {
            console.error('Error checking once-off session status:', error);
            isExpired = true;
          }
        }

        return {
          membership_status: profile.membership_status || 'inactive',
          membership_type: profile.membership_type,
          endDate: isLoyaltyCard || isOnceOff ? null : profile.membership_end_date,
          isExpired,
          latestPayment: payment,
          timeRemaining: isLoyaltyCard
            ? `${sessionsRemaining} sessions remaining`
            : isOnceOff
              ? isExpired ? 'Session used' : '1 session remaining'
              : calculateTimeRemaining(profile.membership_end_date),
          sessionsUsed,
          sessionsRemaining,
          sessions
        };
      } catch (error) {
        console.error('Error fetching membership:', error);
        return {
          membership_status: 'error',
          isExpired: true,
          timeRemaining: 'Error fetching membership details',
          latestPayment: null,
          sessionsUsed: 0
        };
      }
    },
    enabled: !!effectiveUserId,
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Function to manually refresh membership data
  const refreshMembership = useCallback(() => {
    refetch();

    // Also invalidate related queries
    queryClient.invalidateQueries({
      queryKey: [MEMBERSHIP_KEY, effectiveUserId],
      refetchType: 'all'
    });
    queryClient.invalidateQueries({
      queryKey: ['loyalty_sessions', effectiveUserId],
      refetchType: 'all'
    });
    queryClient.invalidateQueries({
      queryKey: ['loyalty_card_status', effectiveUserId],
      refetchType: 'all'
    });
  }, [effectiveUserId, queryClient, refetch]);

  // Check if we should refresh membership data (set by Membership component)
  useEffect(() => {
    const shouldRefresh = sessionStorage.getItem('refreshMembership') === 'true';
    if (shouldRefresh) {
      console.log('Refreshing membership data due to refresh flag');
      sessionStorage.removeItem('refreshMembership'); // Clear the flag
      refreshMembership();
    }
  }, [refreshMembership]);

  return {
    membershipDetails: membershipDetails || {
      membership_status: 'inactive',
      isExpired: true,
      timeRemaining: 'No active membership'
    },
    loading: isLoading,
    refreshMembership
  };
};
