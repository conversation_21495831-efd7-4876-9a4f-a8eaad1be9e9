import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

const USERS_KEY = 'users';
const CLASSES_KEY = 'classes';

export const useUsers = () => {
  return useQuery({
    queryKey: [USERS_KEY],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles_with_loyalty_sessions')
        .select('*');

      if (error) throw error;
      return data;
    }
  });
};

export const useUser = (userId) => {
  return useQuery({
    queryKey: [USERS_KEY, userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!userId
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, updates }) => {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [USERS_KEY] });
      queryClient.invalidateQueries({ queryKey: [USERS_KEY, data.id] });
    }
  });
};

export const useToggleUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, role }) => {
      const { data, error } = await supabase
        .from('profiles')
        .update({ [role]: true })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [USERS_KEY] });
      queryClient.invalidateQueries({ queryKey: [USERS_KEY, data.id] });
    }
  });
};

export const useToggleAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId) => {
      const { data, error } = await supabase
        .rpc('toggle_user_admin', { user_id: userId });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USERS_KEY] });
    }
  });
};

export const useToggleCoach = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId) => {
      const { data, error } = await supabase
        .rpc('toggle_user_coach', { target_user_id: userId });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USERS_KEY] });
    }
  });
};

export const useClasses = () => {
  return useQuery({
    queryKey: [CLASSES_KEY],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('classes')
        .select('*, coach:profiles!coach_id(*)')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    }
  });
};

export const useClass = (classId) => {
  return useQuery({
    queryKey: [CLASSES_KEY, classId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('classes')
        .select('*, coach:profiles!coach_id(*)')
        .eq('id', classId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!classId
  });
};

export const useCreateClass = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (classData) => {
      const { data, error } = await supabase
        .from('classes')
        .insert(classData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASSES_KEY] });
    }
  });
};

export const useUpdateClass = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ classId, updates }) => {
      const { data, error } = await supabase
        .from('classes')
        .update(updates)
        .eq('id', classId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [CLASSES_KEY] });
      queryClient.invalidateQueries({ queryKey: [CLASSES_KEY, data.id] });
    }
  });
};

export const useDeleteClass = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (classId) => {
      // First get the class to ensure it exists
      const { data: classData, error: fetchError } = await supabase
        .from('classes')
        .select('*')
        .eq('id', classId)
        .single();

      if (fetchError) throw fetchError;

      const { error: deleteError } = await supabase
        .from('classes')
        .delete()
        .eq('id', classId);

      if (deleteError) throw deleteError;
      return classData;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [CLASSES_KEY] });
      queryClient.invalidateQueries({ queryKey: [CLASSES_KEY, data.id] });
    }
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId) => {
      const { data, error } = await supabase
        .rpc('delete_user', {
          target_user_id: userId
        });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.message || 'Failed to delete user');
      }

      return {
        userId,
        message: data.message,
        deletedRecords: data.deleted_records
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USERS_KEY] });
    }
  });
};