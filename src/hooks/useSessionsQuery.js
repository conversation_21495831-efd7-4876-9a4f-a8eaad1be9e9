import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

const SESSIONS_KEY = 'sessions';

export const useSessions = ({
  startDate,
  endDate,
  school,
  includeAllSchools = false,
  refetchInterval,
  refetchOnWindowFocus,
  enabled = true
}) => {
  return useQuery({
    queryKey: [SESSIONS_KEY, startDate, endDate, school, includeAllSchools],
    queryFn: async () => {
      // First get the sessions
      let query = supabase
        .from('sessions')
        .select('*, coach:profiles!coach_id(id, first_name, last_name)')
        .gte('start_time', startDate)
        .lte('start_time', endDate)
        .order('start_time', { ascending: true });

      // If not an admin user and school is provided, filter by school
      if (!includeAllSchools && school) {
        query = query.eq('school', school);
      }

      const { data: sessions, error } = await query;
      if (error) throw error;

      if (!sessions || sessions.length === 0) return [];

      // Get all session IDs
      const sessionIds = sessions.map(session => session.id);

      // Fetch all bookings for these sessions in a single query with user details
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings_with_users')
        .select('*')
        .in('session_id', sessionIds);

      if (bookingsError) throw bookingsError;

      // Check if we have the target session ID in our list
      const targetSessionId = '0757c459-3bc5-4249-8b2a-c3b3a258aa59';
      if (sessionIds.includes(targetSessionId)) {
        console.log('TARGET SESSION ID FOUND IN SESSIONS QUERY');

        // Get all bookings specifically for the target session
        const targetBookings = bookings.filter(b => b.session_id === targetSessionId);
        console.log('TARGET SESSION BOOKINGS FROM QUERY:', targetBookings);

        // Get confirmed bookings for the target session
        const confirmedTargetBookings = targetBookings.filter(b => b.status === 'confirmed');
        console.log('CONFIRMED TARGET SESSION BOOKINGS FROM QUERY:', confirmedTargetBookings);
        console.log('CONFIRMED TARGET SESSION BOOKINGS COUNT:', confirmedTargetBookings.length);
      }

      // Attach bookings to their respective sessions
      const sessionsWithBookings = sessions.map(session => {
        const sessionBookings = bookings.filter(booking => booking.session_id === session.id);

        // Debug: Log bookings for each session
        console.log(`useSessionsQuery: Session ${session.id} (${session.title}) has ${sessionBookings.length} bookings`);
        console.log(`useSessionsQuery: Confirmed bookings: ${sessionBookings.filter(b => b.status === 'confirmed').length}`);

        return {
          ...session,
          bookings: sessionBookings
        };
      });

      // Debug: Log the final data
      console.log('useSessionsQuery: Final sessions data:', sessionsWithBookings);

      return sessionsWithBookings || [];
    },
    enabled: enabled && !!startDate && !!endDate, // Only require dates, not school
    refetchInterval, // Add refetch interval if provided
    refetchOnWindowFocus, // Add refetch on window focus if provided
    staleTime: 10000, // Consider data stale after 10 seconds
    cacheTime: 15 * 60 * 1000 // Cache for 15 minutes
  });
};

export const useSession = (sessionId) => {
  return useQuery({
    queryKey: [SESSIONS_KEY, sessionId],
    queryFn: async () => {
      // Get the session
      const { data: session, error } = await supabase
        .from('sessions')
        .select('*, coach:profiles!coach_id(id, first_name, last_name)')
        .eq('id', sessionId)
        .single();

      if (error) throw error;

      // Get all bookings for this session with user details
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings_with_users')
        .select('*')
        .eq('session_id', sessionId);

      if (bookingsError) throw bookingsError;

      // Debug: Log bookings for this session
      console.log(`useSession: Session ${sessionId} (${session.title}) has ${bookings.length} bookings`);
      console.log(`useSession: Confirmed bookings: ${bookings.filter(b => b.status === 'confirmed').length}`);
      console.log('useSession: All bookings:', bookings);

      // Attach bookings to the session
      const sessionWithBookings = {
        ...session,
        bookings: bookings || []
      };

      console.log('useSession: Final session data:', sessionWithBookings);

      return sessionWithBookings;
    },
    enabled: !!sessionId
  });
};

export const useCreateSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (sessionData) => {
      const { data, error } = await supabase
        .from('sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Invalidate all queries that start with the sessions key
      // This ensures all session queries are refreshed regardless of their parameters
      queryClient.invalidateQueries({
        queryKey: [SESSIONS_KEY],
        refetchType: 'all',
        exact: false
      });
    }
  });
};

export const useUpdateSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ sessionId, updates }) => {
      const { data, error } = await supabase
        .from('sessions')
        .update(updates)
        .eq('id', sessionId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Invalidate all queries that start with the sessions key
      queryClient.invalidateQueries({
        queryKey: [SESSIONS_KEY],
        refetchType: 'all',
        exact: false
      });
      // Also specifically invalidate the individual session query
      queryClient.invalidateQueries({
        queryKey: [SESSIONS_KEY, data.id],
        refetchType: 'all'
      });
    }
  });
};

export const useDeleteSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (sessionId) => {
      // First get the session to ensure it exists
      const { data: session, error: fetchError } = await supabase
        .from('sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (fetchError) throw fetchError;

      const { error: deleteError } = await supabase
        .from('sessions')
        .delete()
        .eq('id', sessionId);

      if (deleteError) throw deleteError;
      return session;
    },
    onSuccess: (data) => {
      // Invalidate all queries that start with the sessions key
      queryClient.invalidateQueries({
        queryKey: [SESSIONS_KEY],
        refetchType: 'all',
        exact: false
      });
      // Also specifically invalidate the individual session query
      queryClient.invalidateQueries({
        queryKey: [SESSIONS_KEY, data.id],
        refetchType: 'all'
      });
    }
  });
};