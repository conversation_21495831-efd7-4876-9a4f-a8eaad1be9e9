import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

const LOYALTY_CARDS_KEY = ['loyalty_cards'];

export const useLoyaltyCards = () => {
  return useQuery({
    queryKey: LOYALTY_CARDS_KEY,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('loyalty_cards')
        .select(`
          *,
          users!user_id (
            id
          ),
          profiles!users!user_id!id (
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    }
  });
};

export const useUserLoyaltyCard = (userId) => {
  return useQuery({
    queryKey: [...LOYALTY_CARDS_KEY, 'user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('loyalty_cards')
        .select(`
          *,
          loyalty_sessions (
            id,
            date,
            status
          )
        `)
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!userId
  });
};

export const useCreateLoyaltyCard = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (cardData) => {
      const { data, error } = await supabase
        .from('loyalty_cards')
        .insert([cardData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: LOYALTY_CARDS_KEY });
      queryClient.invalidateQueries({ queryKey: [...LOYALTY_CARDS_KEY, 'user', data.user_id] });
    }
  });
};

export const useUpdateLoyaltyCard = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }) => {
      const { data, error } = await supabase
        .from('loyalty_cards')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: LOYALTY_CARDS_KEY });
      queryClient.invalidateQueries({ queryKey: [...LOYALTY_CARDS_KEY, 'user', data.user_id] });
    }
  });
};

// Loyalty Sessions
const LOYALTY_SESSIONS_KEY = ['loyalty_sessions'];

export const useLoyaltyCardSessions = (cardId) => {
  return useQuery({
    queryKey: [...LOYALTY_SESSIONS_KEY, cardId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('loyalty_sessions')
        .select('*')
        .eq('loyalty_card_id', cardId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!cardId
  });
};

export const useCreateLoyaltySession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (sessionData) => {
      const { data, error } = await supabase
        .from('loyalty_sessions')
        .insert([sessionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: LOYALTY_SESSIONS_KEY });
      queryClient.invalidateQueries({ queryKey: [...LOYALTY_SESSIONS_KEY, data.loyalty_card_id] });
    }
  });
};