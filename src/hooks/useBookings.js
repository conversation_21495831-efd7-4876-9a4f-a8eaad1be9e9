import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

const BOOKINGS_KEY = ['bookings'];

export const useBookings = () => {
  return useQuery({
    queryKey: BOOKINGS_KEY,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings_with_users')
        .select(`
          id,
          session_id,
          user_id,
          participant_count,
          status,
          created_at,
          session_start_time,
          session_end_time,
          session_title,
          session_school,
          user_email,
          user_first_name,
          user_last_name,
          all_users
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    }
  });
};

export const useBooking = (id) => {
  return useQuery({
    queryKey: [...BOOKINGS_KEY, id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings_with_users')
        .select(`
          id,
          session_id,
          user_id,
          participant_count,
          status,
          created_at,
          session_start_time,
          session_end_time,
          session_title,
          session_school,
          user_email,
          user_first_name,
          user_last_name,
          all_users
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id
  });
};

export const useCreateBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (bookingData) => {
      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: BOOKINGS_KEY });
    }
  });
};

export const useUpdateBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }) => {
      const { data, error } = await supabase
        .from('bookings')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKINGS_KEY });
      queryClient.invalidateQueries({ queryKey: [...BOOKINGS_KEY, data.id] });
    }
  });
};

export const useDeleteBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id) => {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: BOOKINGS_KEY });
    }
  });
};