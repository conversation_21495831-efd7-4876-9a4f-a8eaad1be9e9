
import { createContext, useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { supabase, supabaseAdmin } from '../lib/supabase';

export const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    // Check active sessions and sets the user
    const getSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Session error:', error);
          if (mounted) {
            setUser(null);
            setLoading(false);
          }
          return;
        }

        if (mounted) {
          setUser(session?.user ?? null);
          setLoading(false);
        }
      } catch (err) {
        console.error('Failed to get session:', err);
        if (mounted) {
          setUser(null);
          setLoading(false);
        }
      }
    };

    getSession();

    // Listen for changes on auth state
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (!mounted) return;
      
      setUser(session?.user ?? null);
      if (!initialized) {
        setInitialized(true);
        setLoading(false);
      }
    });

    return () => {
      mounted = false;
      subscription?.unsubscribe();
    };
  }, [initialized]);

  const signUp = async ({ email, password, firstName, lastName, school }) => {
    try {
      // 1. Create auth user with metadata (using normal client)
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            school: school,
            full_name: `${firstName} ${lastName}`
          }
        }
      });

      if (authError) throw authError;

      // Profile creation is now handled in SignupStepper after successful auth user creation.

      return { data: authData, error: null };
    } catch (error) {
      console.error('Error signing up:', error);
      return { data: null, error };
    }
  };

  const signIn = useCallback(async ({ email, password }) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error signing in:', error.message);
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setUser(null);
      return { error: null };
    } catch (error) {
      console.error('Error signing out:', error.message);
      return { error };
    } finally {
      setLoading(false);
    }
  }, []);

  const signInWithGoogle = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error signing in with Google:', error.message);
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProfile = useCallback(async (metadata) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.updateUser({
        data: metadata
      });

      if (error) throw error;
      
      // Update the local user state with new metadata
      setUser(prev => ({
        ...prev,
        user_metadata: {
          ...prev.user_metadata,
          ...metadata
        }
      }));

      return { data, error: null };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  }, []);

  const value = {
    user,
    loading,
    signUp: async (credentials) => {
      try {
        const result = await signUp(credentials);
        return result;
      } catch (error) {
        console.error('Error signing up:', error);
        return { data: null, error };
      }
    },
    signIn: async (credentials) => {
      try {
        const result = await signIn(credentials);
        return result;
      } catch (error) {
        console.error('Error signing in:', error);
        return { data: null, error };
      }
    },
    signOut,
    signInWithGoogle,
    updateProfile
  };

  if (!initialized) {
    return null;
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired
};

export default AuthContext;
