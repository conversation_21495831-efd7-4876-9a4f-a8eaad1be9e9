import React, { createContext, useContext, useState, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';

const AdminContext = createContext();

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

export const AdminProvider = ({ children }) => {
  const { session } = useAuth();
  const [classes, setClasses] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Helper function to get auth headers
  const getAuthHeaders = useCallback(() => {
    const token = session?.access_token || localStorage.getItem('sb-xtxrmvkyfghexlzgnpnz-auth-token');
    if (!token) {
      console.error('No access token available');
      throw new Error('Authentication required');
    }
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }, [session]);

  // Classes Management
  const fetchClasses = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/adminManagementBoard/classes', {
        headers: getAuthHeaders()
      });
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  const createClass = useCallback(async (classData) => {
    try {
      setLoading(true);
      const response = await fetch('/api/adminManagementBoard/classes', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(classData)
      });
      if (!response.ok) throw new Error('Failed to create class');
      const newClass = await response.json();
      setClasses(prev => [...prev, newClass]);
      return newClass;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  const updateClass = useCallback(async (classId, updates) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/adminManagementBoard/classes/${classId}`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(updates)
      });
      if (!response.ok) throw new Error('Failed to update class');
      const updatedClass = await response.json();
      setClasses(prev => prev.map(c => c._id === classId ? updatedClass : c));
      return updatedClass;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  const deleteClass = useCallback(async (classId) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/adminManagementBoard/classes/${classId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });
      if (!response.ok) throw new Error('Failed to delete class');
      setClasses(prev => prev.filter(c => c._id !== classId));
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  // Users Management
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_all_users');
      
      if (error) throw error;
      setUsers(data);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const toggleAdmin = useCallback(async (userId) => {
    try {
      setLoading(true);
      const { error } = await supabase
        .rpc('toggle_user_admin', { user_id: userId });

      if (error) throw error;
      await fetchUsers(); // Refresh user list
    } catch (err) {
      console.error('Error toggling admin:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchUsers]);

  const toggleCoach = useCallback(async (userId) => {
    try {
      setLoading(true);
      const { error } = await supabase
        .rpc('toggle_user_coach', { target_user_id: userId });

      if (error) throw error;
      await fetchUsers(); // Refresh user list
    } catch (err) {
      console.error('Error toggling coach:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchUsers]);

  const deleteUser = useCallback(async (userId) => {
    try {
      const { data, error } = await supabase.rpc('delete_user', {
        target_user_id: userId
      });

      if (error) {
        throw new Error(error.message || 'Failed to delete user');
      }

      if (!data?.success) {
        throw new Error(data?.message || 'Failed to delete user');
      }

      // After successful deletion, refresh the users list
      await fetchUsers();

      return {
        success: true,
        message: data.message,
        deletedRecords: data.deleted_records
      };
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }, [fetchUsers]);

  const value = {
    users,
    loading,
    error,
    fetchUsers,
    fetchClasses,
    toggleAdmin,
    toggleCoach,
    deleteUser
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
};
