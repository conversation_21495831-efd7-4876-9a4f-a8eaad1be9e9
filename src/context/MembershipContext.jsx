import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';
import { getTimeRemaining } from '../utils/membership';

const MembershipContext = createContext();

export const useMembership = () => {
  const context = useContext(MembershipContext);
  if (!context) {
    throw new Error('useMembership must be used within a MembershipProvider');
  }
  return context;
};

export const MembershipProvider = ({ children }) => {
  const { user } = useAuth();
  const [membershipDetails, setMembershipDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMembershipDetails = async () => {
      if (!user) {
        setMembershipDetails(null);
        setLoading(false);
        return;
      }

      try {
        // Fetch the latest payment
        const { data: payments, error: paymentsError } = await supabase
          .from('payments')
          .select('*')
          .eq('user_id', user.id)
          .order('payment_date', { ascending: false })
          .limit(1);

        if (paymentsError) throw paymentsError;

        const latestPayment = payments?.[0];

        if (!latestPayment) {
          setMembershipDetails({
            membership_status: 'inactive',
            isExpired: true,
            timeRemaining: 'No active membership',
            latestPayment: null
          });
          setLoading(false);
          return;
        }

        // Get user profile for membership dates
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('membership_status, membership_start_date, membership_end_date')
          .eq('id', user.id)
          .maybeSingle();

        if (profileError) throw profileError;

        const endDate = profile?.membership_end_date;
        const isExpired = endDate ? new Date(endDate) < new Date() : true;
        const timeRemaining = getTimeRemaining(endDate);

        setMembershipDetails({
          membership_status: profile?.membership_status || 'inactive',
          isExpired,
          timeRemaining,
          endDate,
          latestPayment
        });

      } catch (error) {
        console.error('Error fetching membership details:', error);
        setMembershipDetails({
          membership_status: 'error',
          isExpired: true,
          timeRemaining: 'Error fetching membership details',
          latestPayment: null
        });
      } finally {
        setLoading(false);
      }
    };

    fetchMembershipDetails();

    // Set up real-time subscription for payments
    const paymentsSubscription = supabase
      .channel('payments-changes')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'payments',
          filter: `user_id=eq.${user?.id}`
        }, 
        () => {
          fetchMembershipDetails();
        }
      )
      .subscribe();

    // Set up real-time subscription for profile changes
    const profileSubscription = supabase
      .channel('profile-changes')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'profiles',
          filter: `id=eq.${user?.id}`
        }, 
        () => {
          fetchMembershipDetails();
        }
      )
      .subscribe();

    return () => {
      paymentsSubscription.unsubscribe();
      profileSubscription.unsubscribe();
    };
  }, [user]);

  const value = {
    membershipDetails,
    loading
  };

  return (
    <MembershipContext.Provider value={value}>
      {children}
    </MembershipContext.Provider>
  );
}; 