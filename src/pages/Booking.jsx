import styled from '@emotion/styled';
import { useState, useEffect } from 'react';
import { addDays, format, isSameDay, startOfDay, parseISO } from 'date-fns';
// import logoEveryone from '../assets/logo-everyone.svg';
import { theme } from '../styles/theme';
import SessionsAndBookings from '../components/SessionsAndBookings';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import Spinner from '../components/Spinner';
import LoadingContainer from '../components/LoadingContainer';
import { useBooking } from '../context/BookingContext';
import { useSessions } from '../hooks/useSessionsQuery';

const BookingContainer = styled.div`
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  background-color: ${theme.colors.background};
  font-family: ${theme.fonts.body};
  color: ${theme.colors.text};
  padding-top: 60px; /* Account for fixed navigation */
`;

const Hero = styled.div`
  width: 100%;
  max-width: 100vw;
  background: ${theme.colors.secondary};
  color: ${theme.colors.background};
  padding: ${theme.spacing.xl} ${theme.spacing.md};
  text-align: center;
  position: relative;
  overflow: hidden;

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing['2xl']} ${theme.spacing.xl};
  }
`;

const HeroTitle = styled.h1`
  font-size: ${theme.fontSizes['3xl']};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.primary};
  text-align: center;
  margin-bottom: ${theme.spacing.md};

  span {
    display: block;
    font-size: ${theme.fontSizes.xl};
    color: ${theme.colors.border};
    font-weight: ${theme.fontWeights.medium};
  }
`;

const ContentContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const Calendar = styled.div`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  padding: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.xl};

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }
`;

const WeekNavigation = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.lg};
`;

const NavButton = styled.button`
  background: ${theme.colors.background};
  border: 2px solid ${theme.colors.border};
  color: ${theme.colors.text};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  cursor: pointer;
  font-weight: ${theme.fontWeights.medium};
  transition: all 0.2s;

  &:hover {
    background: ${theme.colors.border};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const WeekTitle = styled.span`
  align-content: center;
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.secondary};
  text-align: center;
  display: inline-block;
`;

const DaysGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: ${theme.spacing.sm};

  @media (max-width: ${theme.breakpoints.md}) {
    gap: ${theme.spacing.xs};
  }
`;

const DayButton = styled.button`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: ${theme.spacing.sm};
  background: ${props => props.$isSelected ? theme.colors.primary + '20' : theme.colors.background};
  border: 2px solid ${props => props.$isSelected ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;

  &:hover {
    background: ${props => props.$isSelected ? theme.colors.primary + '30' : theme.colors.border + '50'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
      background-color: transparent;
    }

  ${props => props.$hasSessions && `
    &:after {
      content: '';
      position: absolute;
      bottom: ${theme.spacing.xs};
      width: 6px;
      height: 6px;
      background: ${theme.colors.primary};
      border-radius: 50%;
    }
  `}
`;

const DayName = styled.span`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.textLight};
  margin-bottom: ${theme.spacing.xs};

  @media (max-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.xs};
  }
`;

const DayNumber = styled.span`
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.text};

  @media (max-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.md};
  }
`;

const Booking = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { fetchUserBookings } = useBooking();
  const [userProfile, setUserProfile] = useState(null);
  const [weekOffset, setWeekOffset] = useState(0);

  // Use the useSessions hook with proper parameters
  const startDate = startOfDay(addDays(new Date(), weekOffset * 7)).toISOString();
  const endDate = addDays(startOfDay(addDays(new Date(), weekOffset * 7)), 7).toISOString();

  const { data: sessions = [], isLoading: sessionsLoading } = useSessions({
    startDate,
    endDate,
    school: userProfile?.school || 'Curro Sitari', // Default to Curro Sitari if no school set
    includeAllSchools: userProfile?.is_admin || false,
    enabled: !!userProfile, // Only fetch when we have the user profile
    refetchInterval: 5000, // Refetch every 5 seconds to check for new sessions
    refetchOnWindowFocus: true // Refetch when window regains focus
  });

  // Debug: log sessions data to see if participant_count updates
  console.log('SESSIONS DATA:', sessions);

  useEffect(() => {
    const loadInitialData = async () => {
      if (user) {
        setLoading(true);
        try {
          // Fetch user profile
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*, is_admin, school')
            .eq('id', user.id)
            .single();

          if (profileError) throw profileError;
          setUserProfile(profile);

          // Fetch bookings
          await fetchUserBookings();
        } catch (error) {
          console.error('Error loading initial data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadInitialData();
  }, [user, fetchUserBookings]);

  const startOfWeek = startOfDay(addDays(new Date(), weekOffset * 7));
  const days = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek, i));

  const hasSessionsOnDay = (date) => {
    return sessions?.some(session =>
      isSameDay(parseISO(session.start_time), date)
    ) || false;
  };

  const handlePreviousWeek = () => {
    setWeekOffset(prev => prev - 1);
  };

  const handleNextWeek = () => {
    setWeekOffset(prev => prev + 1);
  };

  const handleDayClick = (date) => {
    setSelectedDate(date);
  };

  if (loading || sessionsLoading) {
    return (
      <LoadingContainer>
        <Spinner />
      </LoadingContainer>
    );
  }

  return (
    <BookingContainer>
      <Hero>
        {/* <Logo src={logoEveryone} alt="Pickleball Helderberg" /> */}
        <HeroTitle>
          Book a Session
          <span>View available sessions and manage your bookings</span>
        </HeroTitle>
      </Hero>
      <ContentContainer>
        <Calendar>
          <WeekNavigation>
            <NavButton onClick={handlePreviousWeek}>Previous Week</NavButton>
            <WeekTitle>
              {format(days[0], 'MMMM yyyy')}
            </WeekTitle>
            <NavButton onClick={handleNextWeek}>Next Week</NavButton>
          </WeekNavigation>
          <DaysGrid>
            {days.map((day) => (
              <DayButton
                key={day.toISOString()}
                onClick={() => handleDayClick(day)}
                $isSelected={isSameDay(day, selectedDate)}
                $hasSessions={hasSessionsOnDay(day)}
                disabled={loading || sessionsLoading}
              >
                <DayName>{format(day, 'EEE')}</DayName>
                <DayNumber>{format(day, 'd')}</DayNumber>
              </DayButton>
            ))}
          </DaysGrid>
        </Calendar>
        <SessionsAndBookings
          selectedDate={selectedDate}
          sessions={sessions}
          isLoading={loading || sessionsLoading}
        />
      </ContentContainer>
    </BookingContainer>
  );
};

export default Booking;
