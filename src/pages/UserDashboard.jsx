import { useState, useEffect } from 'react';
import { useNavigate, Navigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import logoHelderberg from '../assets/logo-helderberg.svg';
import Profile from '../components/Profile';
import Spinner from '../components/Spinner';
import NextBookedClasses from '../components/NextBookedClasses';
import { getDisplayName } from '../utils/user';
import { useMembership } from '../hooks/useMembership';
import { format } from 'date-fns';
import { useAdmin } from '../hooks/useAdmin';
import { completeLatestPayment } from '../utils/payment';
import { toast } from 'react-hot-toast';
import LoyaltyCard from '../components/LoyaltyCard';

const Card = styled.div`
  background: ${theme.colors.background};
  padding: ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.md};
  border: 1px solid ${theme.colors.border};
`;

// * THIS PAGE IS FOR ALL NON-ADMIN USERS * //

const DashboardContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.background};
  margin-top: 64px; /* Height of navigation */
  padding-bottom: 80px; /* Add space for navigation */
  position: relative;

  @media (min-width: ${theme.breakpoints.md}) {
    padding-bottom: 0;
  }
`;

const SelectButton = styled.button`
  background: ${theme.colors.primary};
  color: ${theme.colors.background};
  border: none;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border-radius: ${theme.borderRadius.sm};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${theme.colors.primaryDark};
  }

  &:disabled {
    background: ${theme.colors.border};
    cursor: not-allowed;
  }
`;

const ExpiredMembershipOverlay = styled.div`
  position: fixed;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);


  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${theme.spacing.xl};
  text-align: center;
`;

const OverlayCard = styled.div`
  background: white;
  padding: ${theme.spacing.xl};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.lg};
  max-width: 500px;
  width: 100%;
`;

const OverlayTitle = styled.h2`
  color: ${theme.colors.error};
  font-size: ${theme.fontSizes['2xl']};
  margin-bottom: ${theme.spacing.lg};
`;

const OverlayText = styled.p`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.lg};
  margin-bottom: ${theme.spacing.xl};
`;

const RenewButton = styled(SelectButton)`
  background: ${theme.colors.error};
  font-size: ${theme.fontSizes.lg};
  padding: ${theme.spacing.md} ${theme.spacing.xl};
  width: 100%;

  &:hover {
    background: ${theme.colors.errorDark};
  }
`;

const Header = styled.header`
  background: ${theme.colors.secondary};
  color: ${theme.colors.background};
  padding: ${theme.spacing['2xl']} ${theme.spacing.xl};
  position: relative;
  overflow: hidden;
  margin-bottom: ${theme.spacing.xl};

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: ${theme.colors.primary};
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 0 100%);
  }
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
`;

const Logo = styled.img`
  height: 60px;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.medium};
`;

const Content = styled.main`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing.md};
  padding-bottom: ${theme.spacing['2xl']}; /* Extra padding at bottom */

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

const TabContainer = styled.div`
  margin-bottom: ${theme.spacing.xl};
`;

const SchoolBanner = styled.div`
  background: ${theme.colors.background};
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.md} ${theme.borderRadius.md} 0 0;
  border-bottom: 1px solid ${theme.colors.border};
  margin-bottom: ${theme.spacing.sm};
  font-weight: ${theme.fontWeights.medium};
  font-size: ${theme.fontSizes.lg};
  color: ${theme.colors.secondary};
`;

const TabList = styled.div`
  display: flex;
  border-bottom: 1px solid ${theme.colors.border};
  margin-bottom: ${theme.spacing.lg};
`;

const Tab = styled.button`
  padding: ${theme.spacing.md} ${theme.spacing.xl};
  background: transparent;
  border: none;
  color: ${props => props.$active ? theme.colors.primary : theme.colors.textLight};
  font-weight: ${props => props.$active ? theme.fontWeights.medium : theme.fontWeights.normal};
  position: relative;
  cursor: pointer;
  transition: color 0.2s ease;

  &:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: ${props => props.$active ? theme.colors.primary : 'transparent'};
    transition: background 0.2s ease;
  }

  &:hover {
    color: ${theme.colors.primary};
  }
`;

const TabContent = styled.div`
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;

  @keyframes fadeIn {
    to { opacity: 1; }
  }
`;

const DashboardGrid = styled.div`
  display: grid;
  gap: ${theme.spacing.xl};
  margin-top: ${theme.spacing.xl};

  @media (max-width: ${theme.breakpoints.lg}) {
    display: flex;
    flex-direction: column-reverse;
    gap: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.lg}) {
    grid-template-columns: 2fr 1fr;
    align-items: start;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xl};

  @media (max-width: ${theme.breakpoints.lg}) {
    gap: ${theme.spacing.lg};
  }
`;

const Section = styled.div`
  background: ${theme.colors.background};
  padding: ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.md};
`;

const SectionTitle = styled.h2`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.md};
`;

// Separate wrapper component to handle admin check
const UserDashboardWrapper = () => {
  const { loading: authLoading } = useAuth();
  const { isAdmin, loading: adminLoading } = useAdmin();
  const { membershipDetails, loading: membershipLoading } = useMembership(); // Get membership loading state here

  const isLoading = authLoading || adminLoading || membershipLoading;

  if (isAdmin && !adminLoading) { // Ensure admin check is complete before redirecting
    return <Navigate to="/admin" replace />;
  }

  // Render the main content component, passing the loading state
  return <UserDashboardContent isLoading={isLoading} membershipDetails={membershipDetails} />;
};

// Main dashboard content component
const UserDashboardContent = ({ isLoading, membershipDetails: initialMembershipDetails }) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('sessions');
  // Use the passed membership details and loading state
  const { membershipDetails: currentMembershipDetails, loading: currentMembershipLoading, refreshMembership } = useMembership(user?.id);
  const [searchParams] = useSearchParams();
  const [paymentProcessed, setPaymentProcessed] = useState(false);

  // Combine initial loading state with ongoing membership loading
  const combinedLoading = isLoading || currentMembershipLoading;
  const finalMembershipDetails = currentMembershipDetails || initialMembershipDetails;

  // Check for payment completion from URL parameters
  useEffect(() => {
    const paymentStatus = searchParams.get('payment_status');
    const paymentComplete = sessionStorage.getItem('paymentComplete') === 'true';

    // If payment was successful and not already processed
    if ((paymentStatus === 'success' || paymentComplete) && !paymentProcessed && user) {
      const processPayment = async () => {
        try {
          console.log('Processing payment completion...');
          const success = await completeLatestPayment(user.id);

          if (success) {
            // Clear the payment flags
            sessionStorage.removeItem('paymentComplete');
            // Refresh membership data
            await refreshMembership();
            setPaymentProcessed(true);
            toast.success('Your membership has been renewed successfully!');
          } else {
           return;
          }
        } catch (error) {
          console.error('Error processing payment:', error);
          toast.error('Failed to update membership status');
        }
      };

      processPayment();
    }
  }, [user, searchParams, paymentProcessed, refreshMembership]);

  // Check if we need to refresh membership data (after payment)
  useEffect(() => {
    const shouldRefresh = sessionStorage.getItem('refreshMembership') === 'true';
    if (shouldRefresh) {
      console.log('Dashboard detected refresh flag, refreshing membership data');
      sessionStorage.removeItem('refreshMembership'); // Clear the flag
      refreshMembership();
    }
  }, [refreshMembership]);
  const navigate = useNavigate();

  // Show loading spinner within the main layout if still loading
  if (combinedLoading) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
            {/* Minimal header info during load */}
            {user && <UserInfo><span>Welcome, {getDisplayName(user)}!</span></UserInfo>}
          </HeaderContent>
        </Header>
        <Content>
          <LoadingContainer>
            <Spinner size="large" text="Loading Your Dashboard..." />
          </LoadingContainer>
        </Content>
      </DashboardContainer>
    );
  }

  if (!user) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          </HeaderContent>
        </Header>
        <Content>
          <LoadingContainer>
            No user found. Please log in.
          </LoadingContainer>
        </Content>
      </DashboardContainer>
    );
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'sessions':
        return (
          <TabContent>
            <NextBookedClasses />
          </TabContent>
        );
      case 'profile':
        return (
          <TabContent>
            <Profile />
          </TabContent>
        );
      default:
        return (
          <TabContent>
            <NextBookedClasses />
          </TabContent>
        );
    }
  };

  const renderMembershipSection = () => {
    if (combinedLoading) { // Use combinedLoading instead of membershipLoading
      return <div>Loading membership details...</div>;
    }

    if (!finalMembershipDetails) { // Use finalMembershipDetails
      return <div>No membership information found</div>;
    }

    // Show loyalty card for SWTC members with loyalty card plan
    if (user?.user_metadata?.school === 'SWTC' &&
        finalMembershipDetails?.latestPayment?.plan_id === 'loyalty-card') { // Use finalMembershipDetails

      return (
        <Card style={{ marginBottom: theme.spacing.lg }}>
          <LoyaltyCard
            memberDetails={{
              id: user.id,
              name: `${user.user_metadata.first_name} ${user.user_metadata.last_name}`,
              endDate: finalMembershipDetails.endDate, // Use finalMembershipDetails
              isValid: finalMembershipDetails.membership_status === 'active', // Use finalMembershipDetails
              sessions: finalMembershipDetails.sessions, // Use finalMembershipDetails
              sessionsUsed: finalMembershipDetails.sessionsUsed, // Use finalMembershipDetails
              sessionsRemaining: finalMembershipDetails.sessionsRemaining // Use finalMembershipDetails
            }}
          />
        </Card>
      );
    }

    return (
      <Card style={{ marginBottom: theme.spacing.lg }}>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Current Plan:</strong>{' '}
          {finalMembershipDetails.latestPayment?.plan_title || 'No active plan'} {/* Use finalMembershipDetails */}
        </div>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Status:</strong>{' '}
          <span style={{
            color: finalMembershipDetails.membership_status === 'active' // Use finalMembershipDetails
              ? theme.colors.success
              : theme.colors.error
          }}>
            {finalMembershipDetails.membership_status?.charAt(0).toUpperCase() + // Use finalMembershipDetails
             finalMembershipDetails.membership_status?.slice(1) || 'Inactive'} {/* Use finalMembershipDetails */}
          </span>
        </div>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Time Remaining:</strong>{' '}
          {finalMembershipDetails.timeRemaining || 'N/A'} {/* Use finalMembershipDetails */}
        </div>
        {finalMembershipDetails.endDate && ( // Use finalMembershipDetails
          <div style={{ marginBottom: theme.spacing.md }}>
            <strong>End Date:</strong>{' '}
            {format(new Date(finalMembershipDetails.endDate), 'MMMM d, yyyy')} {/* Use finalMembershipDetails */}
          </div>
        )}
        {finalMembershipDetails.latestPayment && ( // Use finalMembershipDetails
          <div style={{ marginBottom: theme.spacing.lg }}>
            <strong>Plan Details:</strong>{' '}
            {finalMembershipDetails.latestPayment.amount} ZAR / {finalMembershipDetails.latestPayment.period} {/* Use finalMembershipDetails */}
          </div>
        )}

        {finalMembershipDetails.isExpired && ( // Use finalMembershipDetails
          <SelectButton
            onClick={() => {
              // Store current user details
              const userDetails = {
                firstName: user.user_metadata.first_name,
                lastName: user.user_metadata.last_name,
                email: user.email,
                school: user.user_metadata.school
              };
              sessionStorage.setItem('userDetails', JSON.stringify(userDetails));
              // Set renewal flag
              localStorage.setItem('renew', 'true');
              navigate('/signup?step=plan');
            }}
            style={{
              background: theme.colors.error,
              marginTop: theme.spacing.md
            }}
          >
            {finalMembershipDetails.membership_type === 'once-off' || finalMembershipDetails.membership_type === 'loyalty-card' // Use finalMembershipDetails
              ? 'Purchase New Sessions'
              : 'Renew Membership'}
          </SelectButton>
        )}
      </Card>
    );
  };

  // Early return for expired membership
  if (finalMembershipDetails?.isExpired) { // Use finalMembershipDetails
    // For once-off and loyalty members, don't show the overlay
    const isLoyaltyCard = finalMembershipDetails.membership_type === 'loyalty-card'; // Use finalMembershipDetails
    const isOnceOff = finalMembershipDetails.membership_type === 'once-off'; // Use finalMembershipDetails

    if (isLoyaltyCard || isOnceOff) {
      return (
        <DashboardContainer>
          <Header>
            <HeaderContent>
              <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
              <UserInfo>
                <span>Welcome, {getDisplayName(user)}!</span>
              </UserInfo>
            </HeaderContent>
          </Header>
          <Content>
            <DashboardGrid>
              <MainContent>
                <TabContainer>
                  <SchoolBanner>
                    {user?.user_metadata?.school || 'Your Club'}
                  </SchoolBanner>
                  <TabList>
                    <Tab
                      $active={activeTab === 'sessions'}
                      onClick={() => setActiveTab('sessions')}
                    >
                      Activity
                    </Tab>
                    <Tab
                      $active={activeTab === 'profile'}
                      onClick={() => setActiveTab('profile')}
                    >
                      Profile
                    </Tab>
                  </TabList>
                  {renderContent()}
                </TabContainer>
              </MainContent>
              <div>
                <Section>
                  <SectionTitle>Your Membership</SectionTitle>
                  {renderMembershipSection()}
                </Section>
              </div>
            </DashboardGrid>
          </Content>
        </DashboardContainer>
      );
    }

    // For other membership types, show the overlay
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
            <UserInfo>
              <span>Welcome, {getDisplayName(user)}!</span>
            </UserInfo>
          </HeaderContent>
        </Header>
        <ExpiredMembershipOverlay>
          <OverlayCard>
            <OverlayTitle>Membership Expired</OverlayTitle>
            <OverlayText>
              Your membership has expired. Please renew your membership to continue using the app and booking classes.
            </OverlayText>
            <RenewButton onClick={() => {
              // Store current user details
              const userDetails = {
                firstName: user.user_metadata.first_name,
                lastName: user.user_metadata.last_name,
                email: user.email,
                school: user.user_metadata.school
              };
              sessionStorage.setItem('userDetails', JSON.stringify(userDetails));
              // Set renewal flag
              localStorage.setItem('renew', 'true');
              navigate('/signup?step=plan');
            }}>
              Renew Membership Now
            </RenewButton>
          </OverlayCard>
        </ExpiredMembershipOverlay>
        <Content>
          <DashboardGrid>
            <MainContent>
              <TabContainer>
                <SchoolBanner>
                  {user?.user_metadata?.school || 'Your Club'}
                </SchoolBanner>
                <TabList>
                  <Tab
                    $active={activeTab === 'sessions'}
                    onClick={() => setActiveTab('sessions')}
                  >
                    Activity
                  </Tab>
                  <Tab
                    $active={activeTab === 'profile'}
                    onClick={() => setActiveTab('profile')}
                  >
                    Profile
                  </Tab>
                </TabList>
                {renderContent()}
              </TabContainer>
            </MainContent>
            <div>
              <Section>
                <SectionTitle>Your Membership</SectionTitle>
                {renderMembershipSection()}
              </Section>
            </div>
          </DashboardGrid>
        </Content>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <Header>
        <HeaderContent>
          <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          <UserInfo>
            <span>Welcome, {getDisplayName(user)}!</span>
          </UserInfo>
        </HeaderContent>
      </Header>
      <Content>
        <DashboardGrid>
          <MainContent>
            <TabContainer>
              <SchoolBanner>
                {user?.user_metadata?.school || 'Your Club'}
              </SchoolBanner>
              <TabList>
                <Tab
                  $active={activeTab === 'sessions'}
                  onClick={() => setActiveTab('sessions')}
                >
                  Activity
                </Tab>
                <Tab
                  $active={activeTab === 'profile'}
                  onClick={() => setActiveTab('profile')}
                >
                  Profile
                </Tab>
              </TabList>
              {renderContent()}
            </TabContainer>
          </MainContent>
          <div>
            <Section>
              <SectionTitle>Your Membership</SectionTitle>
              {renderMembershipSection()}
            </Section>
          </div>
        </DashboardGrid>
      </Content>
    </DashboardContainer>
  );
};

// Export the wrapper component
export default UserDashboardWrapper;
