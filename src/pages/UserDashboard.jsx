import { useState, useEffect } from 'react';
import { useNavigate, Navigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import logoHelderberg from '../assets/logo-helderberg.svg';
import Profile from '../components/Profile';
import Spinner from '../components/Spinner';
import NextBookedClasses from '../components/NextBookedClasses';
import { getDisplayName } from '../utils/user';
import { useMembership } from '../hooks/useMembership';
import { format } from 'date-fns';
import { useAdmin } from '../hooks/useAdmin';
import { completeLatestPayment } from '../utils/payment';
import { toast } from 'react-hot-toast';
import LoyaltyCard from '../components/LoyaltyCard';

const Card = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  padding: ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.lg};
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

// * THIS PAGE IS FOR ALL NON-ADMIN USERS * //

const DashboardContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  margin-top: 64px; /* Height of navigation */
  padding-bottom: 80px; /* Add space for navigation */
  position: relative;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  @media (min-width: ${theme.breakpoints.md}) {
    padding-bottom: 0;
  }
`;

const SelectButton = styled.button`
  background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
  color: white;
  border: none;
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: ${theme.fontWeights.semibold};
  font-size: ${theme.fontSizes.sm};
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    background: ${theme.colors.border};
    cursor: not-allowed;
    transform: none;
    box-shadow: none;

    &::before {
      display: none;
    }
  }
`;

const ExpiredMembershipOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${theme.spacing.xl};
  text-align: center;
`;

const OverlayCard = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  padding: ${theme.spacing.xl};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  max-width: 500px;
  width: 100%;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, ${theme.colors.error}, #dc2626);
    border-radius: ${theme.borderRadius.lg} ${theme.borderRadius.lg} 0 0;
  }
`;

const OverlayTitle = styled.h2`
  color: ${theme.colors.error};
  font-size: ${theme.fontSizes['2xl']};
  margin-bottom: ${theme.spacing.lg};
  font-weight: ${theme.fontWeights.bold};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.sm};

  &::before {
    content: '⚠️';
    font-size: ${theme.fontSizes.xl};
  }
`;

const OverlayText = styled.p`
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
  margin-bottom: ${theme.spacing.xl};
  line-height: 1.6;
  font-weight: ${theme.fontWeights.medium};
`;

const RenewButton = styled(SelectButton)`
  background: linear-gradient(135deg, ${theme.colors.error}, #dc2626);
  font-size: ${theme.fontSizes.lg};
  padding: ${theme.spacing.lg} ${theme.spacing.xl};
  width: 100%;
  font-weight: ${theme.fontWeights.bold};

  &:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
  }
`;

const Header = styled.header`
  background: linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.primary});
  color: white;
  padding: ${theme.spacing['2xl']} ${theme.spacing.xl};
  position: relative;
  overflow: hidden;
  margin-bottom: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 0 100%);
  }
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
  gap: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    text-align: center;
  }
`;

const Logo = styled.img`
  height: 60px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));

  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  background: rgba(255, 255, 255, 0.15);
  border-radius: ${theme.borderRadius.lg};
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.semibold};
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  span {
    display: flex;
    align-items: center;
    gap: ${theme.spacing.xs};

    &::before {
      content: '👋';
      font-size: ${theme.fontSizes.md};
    }
  }
`;

const Content = styled.main`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing.md};
  padding-bottom: ${theme.spacing['2xl']}; /* Extra padding at bottom */

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  margin: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  .loading-text {
    margin-top: ${theme.spacing.md};
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.lg};
    font-weight: ${theme.fontWeights.bold};
  }
`;

const TabContainer = styled.div`
  margin-bottom: ${theme.spacing.xl};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

const SchoolBanner = styled.div`
  background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
  padding: ${theme.spacing.lg} ${theme.spacing.xl};
  color: white;
  font-weight: ${theme.fontWeights.bold};
  font-size: ${theme.fontSizes.lg};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  .club-icon {
    font-size: ${theme.fontSizes.xl};
    margin-right: ${theme.spacing.xs};
  }
`;

const TabList = styled.div`
  display: flex;
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
`;

const Tab = styled.button`
  padding: ${theme.spacing.lg} ${theme.spacing.xl};
  background: ${props => props.$active
    ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7))'
    : 'transparent'};
  border: none;
  color: ${props => props.$active ? theme.colors.primary : theme.colors.textLight};
  font-weight: ${props => props.$active ? theme.fontWeights.bold : theme.fontWeights.medium};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  font-size: ${theme.fontSizes.md};
  backdrop-filter: ${props => props.$active ? 'blur(10px)' : 'none'};

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: ${props => props.$active
      ? `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary})`
      : 'transparent'};
    transition: all 0.3s ease;
  }

  &:hover {
    color: ${theme.colors.primary};
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
  }

  &:not(:last-child) {
    border-right: 1px solid rgba(0, 0, 0, 0.05);
  }
`;

const TabContent = styled.div`
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;

  @keyframes fadeIn {
    to { opacity: 1; }
  }
`;

const DashboardGrid = styled.div`
  display: grid;
  gap: ${theme.spacing.xl};
  margin-top: ${theme.spacing.xl};

  @media (max-width: ${theme.breakpoints.lg}) {
    display: flex;
    flex-direction: column-reverse;
    gap: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.lg}) {
    grid-template-columns: 2fr 1fr;
    align-items: start;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xl};

  @media (max-width: ${theme.breakpoints.lg}) {
    gap: ${theme.spacing.lg};
  }
`;

const Section = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  padding: ${theme.spacing.xl};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

const SectionTitle = styled.h2`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.lg};
  color: ${theme.colors.primary};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};

  &::before {
    content: '💳';
    font-size: ${theme.fontSizes.lg};
  }
`;

// Separate wrapper component to handle admin check
const UserDashboardWrapper = () => {
  const { loading: authLoading } = useAuth();
  const { isAdmin, loading: adminLoading } = useAdmin();
  const { membershipDetails, loading: membershipLoading } = useMembership(); // Get membership loading state here

  const isLoading = authLoading || adminLoading || membershipLoading;

  if (isAdmin && !adminLoading) { // Ensure admin check is complete before redirecting
    return <Navigate to="/admin" replace />;
  }

  // Render the main content component, passing the loading state
  return <UserDashboardContent isLoading={isLoading} membershipDetails={membershipDetails} />;
};

// Main dashboard content component
const UserDashboardContent = ({ isLoading, membershipDetails: initialMembershipDetails }) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('sessions');
  // Use the passed membership details and loading state
  const { membershipDetails: currentMembershipDetails, loading: currentMembershipLoading, refreshMembership } = useMembership(user?.id);
  const [searchParams] = useSearchParams();
  const [paymentProcessed, setPaymentProcessed] = useState(false);

  // Combine initial loading state with ongoing membership loading
  const combinedLoading = isLoading || currentMembershipLoading;
  const finalMembershipDetails = currentMembershipDetails || initialMembershipDetails;

  // Check for payment completion from URL parameters
  useEffect(() => {
    const paymentStatus = searchParams.get('payment_status');
    const paymentComplete = sessionStorage.getItem('paymentComplete') === 'true';

    // If payment was successful and not already processed
    if ((paymentStatus === 'success' || paymentComplete) && !paymentProcessed && user) {
      const processPayment = async () => {
        try {
          console.log('Processing payment completion...');
          const success = await completeLatestPayment(user.id);

          if (success) {
            // Clear the payment flags
            sessionStorage.removeItem('paymentComplete');
            // Refresh membership data
            await refreshMembership();
            setPaymentProcessed(true);
            toast.success('Your membership has been renewed successfully!');
          } else {
           return;
          }
        } catch (error) {
          console.error('Error processing payment:', error);
          toast.error('Failed to update membership status');
        }
      };

      processPayment();
    }
  }, [user, searchParams, paymentProcessed, refreshMembership]);

  // Check if we need to refresh membership data (after payment)
  useEffect(() => {
    const shouldRefresh = sessionStorage.getItem('refreshMembership') === 'true';
    if (shouldRefresh) {
      console.log('Dashboard detected refresh flag, refreshing membership data');
      sessionStorage.removeItem('refreshMembership'); // Clear the flag
      refreshMembership();
    }
  }, [refreshMembership]);
  const navigate = useNavigate();

  // Show loading spinner within the main layout if still loading
  if (combinedLoading) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
            {/* Minimal header info during load */}
            {user && <UserInfo><span>Welcome, {getDisplayName(user)}!</span></UserInfo>}
          </HeaderContent>
        </Header>
        <Content>
          <LoadingContainer>
            <Spinner size="large" />
            <div className="loading-text">Loading Your Dashboard...</div>
          </LoadingContainer>
        </Content>
      </DashboardContainer>
    );
  }

  if (!user) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          </HeaderContent>
        </Header>
        <Content>
          <LoadingContainer>
            No user found. Please log in.
          </LoadingContainer>
        </Content>
      </DashboardContainer>
    );
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'sessions':
        return (
          <TabContent>
            <NextBookedClasses />
          </TabContent>
        );
      case 'profile':
        return (
          <TabContent>
            <Profile />
          </TabContent>
        );
      default:
        return (
          <TabContent>
            <NextBookedClasses />
          </TabContent>
        );
    }
  };

  const renderMembershipSection = () => {
    if (combinedLoading) { // Use combinedLoading instead of membershipLoading
      return <div>Loading membership details...</div>;
    }

    if (!finalMembershipDetails) { // Use finalMembershipDetails
      return <div>No membership information found</div>;
    }

    // Show loyalty card for SWTC members with loyalty card plan
    if (user?.user_metadata?.school === 'SWTC' &&
        finalMembershipDetails?.latestPayment?.plan_id === 'loyalty-card') { // Use finalMembershipDetails

      return (
        <Card style={{ marginBottom: theme.spacing.lg }}>
          <LoyaltyCard
            memberDetails={{
              id: user.id,
              name: `${user.user_metadata.first_name} ${user.user_metadata.last_name}`,
              endDate: finalMembershipDetails.endDate, // Use finalMembershipDetails
              isValid: finalMembershipDetails.membership_status === 'active', // Use finalMembershipDetails
              sessions: finalMembershipDetails.sessions, // Use finalMembershipDetails
              sessionsUsed: finalMembershipDetails.sessionsUsed, // Use finalMembershipDetails
              sessionsRemaining: finalMembershipDetails.sessionsRemaining // Use finalMembershipDetails
            }}
          />
        </Card>
      );
    }

    return (
      <Card style={{ marginBottom: theme.spacing.lg }}>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Current Plan:</strong>{' '}
          {finalMembershipDetails.latestPayment?.plan_title || 'No active plan'} {/* Use finalMembershipDetails */}
        </div>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Status:</strong>{' '}
          <span style={{
            color: finalMembershipDetails.membership_status === 'active' // Use finalMembershipDetails
              ? theme.colors.success
              : theme.colors.error
          }}>
            {finalMembershipDetails.membership_status?.charAt(0).toUpperCase() + // Use finalMembershipDetails
             finalMembershipDetails.membership_status?.slice(1) || 'Inactive'} {/* Use finalMembershipDetails */}
          </span>
        </div>
        <div style={{ marginBottom: theme.spacing.md }}>
          <strong>Time Remaining:</strong>{' '}
          {finalMembershipDetails.timeRemaining || 'N/A'} {/* Use finalMembershipDetails */}
        </div>
        {finalMembershipDetails.endDate && ( // Use finalMembershipDetails
          <div style={{ marginBottom: theme.spacing.md }}>
            <strong>End Date:</strong>{' '}
            {format(new Date(finalMembershipDetails.endDate), 'MMMM d, yyyy')} {/* Use finalMembershipDetails */}
          </div>
        )}
        {finalMembershipDetails.latestPayment && ( // Use finalMembershipDetails
          <div style={{ marginBottom: theme.spacing.lg }}>
            <strong>Plan Details:</strong>{' '}
            {finalMembershipDetails.latestPayment.amount} ZAR / {finalMembershipDetails.latestPayment.period} {/* Use finalMembershipDetails */}
          </div>
        )}

        {finalMembershipDetails.isExpired && ( // Use finalMembershipDetails
          <SelectButton
            onClick={() => {
              // Store current user details
              const userDetails = {
                firstName: user.user_metadata.first_name,
                lastName: user.user_metadata.last_name,
                email: user.email,
                school: user.user_metadata.school
              };
              sessionStorage.setItem('userDetails', JSON.stringify(userDetails));
              // Set renewal flag
              localStorage.setItem('renew', 'true');
              navigate('/signup?step=plan');
            }}
            style={{
              background: theme.colors.error,
              marginTop: theme.spacing.md
            }}
          >
            {finalMembershipDetails.membership_type === 'once-off' || finalMembershipDetails.membership_type === 'loyalty-card' // Use finalMembershipDetails
              ? 'Purchase New Sessions'
              : 'Renew Membership'}
          </SelectButton>
        )}
      </Card>
    );
  };

  // Early return for expired membership
  if (finalMembershipDetails?.isExpired) { // Use finalMembershipDetails
    // For once-off and loyalty members, don't show the overlay
    const isLoyaltyCard = finalMembershipDetails.membership_type === 'loyalty-card'; // Use finalMembershipDetails
    const isOnceOff = finalMembershipDetails.membership_type === 'once-off'; // Use finalMembershipDetails

    if (isLoyaltyCard || isOnceOff) {
      return (
        <DashboardContainer>
          <Header>
            <HeaderContent>
              <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
              <UserInfo>
                <span>Welcome, {getDisplayName(user)}!</span>
              </UserInfo>
            </HeaderContent>
          </Header>
          <Content>
            <DashboardGrid>
              <MainContent>
                <TabContainer>
                  <SchoolBanner>
                    <span className="club-icon">🏓</span>
                    {user?.user_metadata?.school || 'Your Club'}
                  </SchoolBanner>
                  <TabList>
                    <Tab
                      $active={activeTab === 'sessions'}
                      onClick={() => setActiveTab('sessions')}
                    >
                      Activity
                    </Tab>
                    <Tab
                      $active={activeTab === 'profile'}
                      onClick={() => setActiveTab('profile')}
                    >
                      Profile
                    </Tab>
                  </TabList>
                  {renderContent()}
                </TabContainer>
              </MainContent>
              <div>
                <Section>
                  <SectionTitle>Your Membership</SectionTitle>
                  {renderMembershipSection()}
                </Section>
              </div>
            </DashboardGrid>
          </Content>
        </DashboardContainer>
      );
    }

    // For other membership types, show the overlay
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
            <UserInfo>
              <span>Welcome, {getDisplayName(user)}!</span>
            </UserInfo>
          </HeaderContent>
        </Header>
        <ExpiredMembershipOverlay>
          <OverlayCard>
            <OverlayTitle>Membership Expired</OverlayTitle>
            <OverlayText>
              Your membership has expired. Please renew your membership to continue using the app and booking classes.
            </OverlayText>
            <RenewButton onClick={() => {
              // Store current user details
              const userDetails = {
                firstName: user.user_metadata.first_name,
                lastName: user.user_metadata.last_name,
                email: user.email,
                school: user.user_metadata.school
              };
              sessionStorage.setItem('userDetails', JSON.stringify(userDetails));
              // Set renewal flag
              localStorage.setItem('renew', 'true');
              navigate('/signup?step=plan');
            }}>
              Renew Membership Now
            </RenewButton>
          </OverlayCard>
        </ExpiredMembershipOverlay>
        <Content>
          <DashboardGrid>
            <MainContent>
              <TabContainer>
                <SchoolBanner>
                  <span className="club-icon">🏓</span>
                  {user?.user_metadata?.school || 'Your Club'}
                </SchoolBanner>
                <TabList>
                  <Tab
                    $active={activeTab === 'sessions'}
                    onClick={() => setActiveTab('sessions')}
                  >
                    Activity
                  </Tab>
                  <Tab
                    $active={activeTab === 'profile'}
                    onClick={() => setActiveTab('profile')}
                  >
                    Profile
                  </Tab>
                </TabList>
                {renderContent()}
              </TabContainer>
            </MainContent>
            <div>
              <Section>
                <SectionTitle>Your Membership</SectionTitle>
                {renderMembershipSection()}
              </Section>
            </div>
          </DashboardGrid>
        </Content>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <Header>
        <HeaderContent>
          <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          <UserInfo>
            <span>Welcome, {getDisplayName(user)}!</span>
          </UserInfo>
        </HeaderContent>
      </Header>
      <Content>
        <DashboardGrid>
          <MainContent>
            <TabContainer>
              <SchoolBanner>
                <span className="club-icon">🏓</span>
                {user?.user_metadata?.school || 'Your Club'}
              </SchoolBanner>
              <TabList>
                <Tab
                  $active={activeTab === 'sessions'}
                  onClick={() => setActiveTab('sessions')}
                >
                  Activity
                </Tab>
                <Tab
                  $active={activeTab === 'profile'}
                  onClick={() => setActiveTab('profile')}
                >
                  Profile
                </Tab>
              </TabList>
              {renderContent()}
            </TabContainer>
          </MainContent>
          <div>
            <Section>
              <SectionTitle>Your Membership</SectionTitle>
              {renderMembershipSection()}
            </Section>
          </div>
        </DashboardGrid>
      </Content>
    </DashboardContainer>
  );
};

// Export the wrapper component
export default UserDashboardWrapper;
