import styled from '@emotion/styled';
import { CalendarIcon, ChartBarIcon, CogIcon, CurrencyDollarIcon, UsersIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import logoHelderberg from '../assets/logo-helderberg.svg';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import { getDisplayName } from '../utils/user';
import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';
import { BookingsList } from '../components/BookingsList';
import Spinner from '../components/Spinner';

//* ONLY ADMIN CAN ACCESS THIS PAGE *//

const DashboardContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.background};
  padding-top: 80px; /* Add padding to account for fixed navigation */
`;

const Header = styled.header`
  background: white;
  padding: ${theme.spacing.lg} ${theme.spacing.xl};
  box-shadow: ${theme.shadows.sm};
  `;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled.img`
  height: 40px;
`;

const UserInfo = styled.div`
  text-align: right;
`;

const Welcome = styled.h2`
  font-family: ${theme.fonts.heading};
  font-size: ${theme.fontSizes.xl};
  color: ${theme.colors.primary};
  margin: 0;
`;

const Role = styled.p`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.text};
  margin: 0;
`;

const Content = styled.main`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing.xl};
`;

const Title = styled.h1`
  font-family: ${theme.fonts.heading};
  font-size: ${theme.fontSizes['3xl']};
  color: ${theme.colors.primary};
  margin: 0 0 ${theme.spacing.xl} 0;
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${theme.spacing.lg};
`;

const Card = styled.div`
  background: white;
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.lg};
  }
`;

const MenuLink = styled(Link)`
  display: block;
  padding: ${theme.spacing.lg};
  color: inherit;
  text-decoration: none;

  h2 {
    font-family: ${theme.fonts.heading};
    font-size: ${theme.fontSizes.xl};
    color: ${theme.colors.primary};
    margin: 0 0 ${theme.spacing.sm} 0;
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};

    svg {
      width: 24px;
      height: 24px;
    }
  }

  p {
    margin: 0;
    font-size: ${theme.fontSizes.sm};
    color: ${theme.colors.text};
  }
`;

const Stats = styled.div`
  margin-top: ${theme.spacing.lg};
  padding-top: ${theme.spacing.md};
  border-top: 1px solid ${theme.colors.border};
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.text};
`;

const LoadingContainer = styled.div` 
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
`;

const AdminDashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalSessions: 0,
    activeSessions: 0,
    totalPlayers: 0,
    error: null,
    loading: true
  });
  const [selectedSession, setSelectedSession] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const { data: totalUsers, error: usersError } = await supabase.rpc('get_total_users_count');
        if (usersError) throw usersError;

        const { data: totalSessions, error: sessionsError } = await supabase.rpc('get_total_sessions_count');
        if (sessionsError) throw sessionsError;

        const { data: activeSessions, error: activeSessionsError } = await supabase.rpc('get_active_sessions_count');
        if (activeSessionsError) throw activeSessionsError;

        const { data: totalPlayers, error: playersError } = await supabase.rpc('get_total_players_count');
        if (playersError) throw playersError;

        setStats({
          totalUsers,
          totalSessions,
          activeSessions,
          totalPlayers,
          error: null,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        toast.error('Failed to load dashboard stats');
        setStats({
          totalUsers: 0,
          totalSessions: 0,
          activeSessions: 0,
          totalPlayers: 0,
          error: error.message,
          loading: false
        });
      }
    };

    fetchStats();
  }, []);

  const menuItems = [
    {
      title: 'User Management',
      description: 'Manage users and their roles',
      path: '/admin/manage?tab=users',
      state: 'users',
      icon: UsersIcon,
      stats: `${stats.totalUsers} Active Users`
    },
    {
      title: 'Session Management',
      description: 'Create and manage Pickleball sessions',
      path: '/admin/manage?tab=classes',
      state: 'classes',
      icon: CalendarIcon,
      stats: `${stats.activeSessions} Active Sessions`,
      onClick: () => {
        // You would need to fetch and set the selected session here
        setSelectedSession(/* session data */);
      }
    },
    {
      title: 'Analytics Dashboard',
      description: 'View detailed analytics and insights',
      path: '/admin/manage?tab=analytics',
      state: 'analytics',
      icon: ChartBarIcon,
      stats: `${stats.totalSessions} Total Sessions`
    },
    {
      title: 'Player Management',
      description: 'Manage player profiles and bookings',
      path: '/admin/manage?tab=players',
      state: 'players',
      icon: CurrencyDollarIcon,
      stats: `${stats.totalPlayers} Active Players`
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      path: '/admin/manage?tab=settings',
      state: 'settings',
      icon: CogIcon,
      stats: 'Manage Settings'
    }
  ];

  if (authLoading || stats.loading) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          </HeaderContent>
        </Header>
        <LoadingContainer>
          <Spinner size="large" text="Loading Admin Data..." />
        </LoadingContainer>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <Header>
        <HeaderContent>
          <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          <UserInfo>
            <Welcome>Welcome, {getDisplayName(user)}</Welcome>
            <Role>Administrator</Role>
          </UserInfo>
        </HeaderContent>
      </Header>
      <Content>
        <Title>Admin Dashboard</Title>
        {stats.error ? (
          <p style={{ color: 'red' }}>Error loading stats: {stats.error}</p>
        ) : (
          <DashboardGrid>
            {menuItems.map((item) => (
              <Card key={item.state}>
                <MenuLink 
                  to={item.path}
                  state={{ activeTab: item.state }}
                >
                  <h2>
                    <item.icon />
                    {item.title}
                  </h2>
                  <p>{item.description}</p>
                  <Stats>{item.stats}</Stats>
                </MenuLink>
              </Card>
            ))}
          </DashboardGrid>
        )}
        {selectedSession && (
          <BookingsList sessionId={selectedSession.id} />
        )}
      </Content>
    </DashboardContainer>
  );
};

export default AdminDashboard;
