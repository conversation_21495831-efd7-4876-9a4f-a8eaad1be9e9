import styled from '@emotion/styled';
import { CalendarIcon, ChartBarIcon, CogIcon, CurrencyDollarIcon, UsersIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import logoHelderberg from '../assets/logo-helderberg.svg';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import { getDisplayName } from '../utils/user';
import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';
import { BookingsList } from '../components/BookingsList';
import Spinner from '../components/Spinner';

//* ONLY ADMIN CAN ACCESS THIS PAGE *//

const DashboardContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding-top: 80px; /* Add padding to account for fixed navigation */
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
`;

const Header = styled.header`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  padding: ${theme.spacing.lg} ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary});
  }
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    text-align: center;
  }
`;

const Logo = styled.img`
  height: 40px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));

  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
  }
`;

const UserInfo = styled.div`
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};

  @media (max-width: ${theme.breakpoints.sm}) {
    text-align: center;
  }
`;

const Welcome = styled.h2`
  font-family: ${theme.fonts.heading};
  font-size: ${theme.fontSizes.lg};
  color: ${theme.colors.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  font-weight: ${theme.fontWeights.bold};

  &::before {
    content: '👋';
    font-size: ${theme.fontSizes.md};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.xl};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    justify-content: center;
  }
`;

const Role = styled.p`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.textLight};
  margin: 0;
  font-weight: ${theme.fontWeights.semibold};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
  color: white;
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.full};
  font-size: ${theme.fontSizes.xs};
  white-space: nowrap;

  &::before {
    content: '👑';
    font-size: ${theme.fontSizes.xs};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    justify-content: center;
    align-self: center;
  }
`;

const Content = styled.main`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const TitleSection = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  margin-bottom: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

const Title = styled.h1`
  font-family: ${theme.fonts.heading};
  font-size: ${theme.fontSizes['2xl']};
  color: ${theme.colors.primary};
  margin: 0 0 ${theme.spacing.md} 0;
  font-weight: ${theme.fontWeights.bold};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.sm};

  &::before {
    content: '🎯';
    font-size: ${theme.fontSizes.xl};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes['3xl']};
  }
`;

const Subtitle = styled.p`
  color: ${theme.colors.textLight};
  margin: 0;
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.medium};

  &::before {
    content: '⚡';
    margin-right: ${theme.spacing.xs};
  }
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.sm}) {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.lg}) {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: ${theme.spacing.xl};
  }
`;

const Card = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary});
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

    &::before {
      opacity: 1;
    }
  }
`;

const MenuLink = styled(Link)`
  display: block;
  padding: ${theme.spacing.xl};
  color: inherit;
  text-decoration: none;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.md};

  h2 {
    font-family: ${theme.fonts.heading};
    font-size: ${theme.fontSizes.lg};
    color: ${theme.colors.primary};
    margin: 0;
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};
    font-weight: ${theme.fontWeights.bold};

    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes.xl};
    }

    svg {
      width: 28px;
      height: 28px;
      color: ${theme.colors.secondary};
      transition: all 0.3s ease;
      flex-shrink: 0;

      @media (min-width: ${theme.breakpoints.md}) {
        width: 32px;
        height: 32px;
      }
    }
  }

  p {
    margin: 0;
    font-size: ${theme.fontSizes.sm};
    color: ${theme.colors.textLight};
    font-weight: ${theme.fontWeights.medium};
    line-height: 1.5;
    flex: 1;

    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes.md};
    }
  }

  &:hover {
    h2 svg {
      transform: scale(1.1);
      color: ${theme.colors.primary};
    }
  }
`;

const Stats = styled.div`
  margin-top: auto;
  padding: ${theme.spacing.md};
  background: rgba(248, 250, 252, 0.5);
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.primary};
  font-weight: ${theme.fontWeights.bold};
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.xs};
  transition: all 0.3s ease;

  &::before {
    content: '📊';
    font-size: ${theme.fontSizes.sm};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.md};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  margin: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  .loading-text {
    margin-top: ${theme.spacing.md};
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.lg};
    font-weight: ${theme.fontWeights.bold};
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};

    &::before {
      content: '⏳';
      font-size: ${theme.fontSizes.xl};
    }
  }
`;

const ErrorContainer = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  margin: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  text-align: center;

  &::before {
    content: '⚠️';
    display: block;
    font-size: ${theme.fontSizes.xxl};
    margin-bottom: ${theme.spacing.lg};
  }

  h3 {
    color: ${theme.colors.error};
    font-size: ${theme.fontSizes.xl};
    margin: 0 0 ${theme.spacing.md};
    font-weight: ${theme.fontWeights.bold};
  }

  p {
    color: ${theme.colors.textLight};
    margin: 0;
    font-size: ${theme.fontSizes.md};
    font-weight: ${theme.fontWeights.medium};
  }
`;

const AdminDashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalSessions: 0,
    activeSessions: 0,
    totalPlayers: 0,
    error: null,
    loading: true
  });
  const [selectedSession, setSelectedSession] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const { data: totalUsers, error: usersError } = await supabase.rpc('get_total_users_count');
        if (usersError) throw usersError;

        const { data: totalSessions, error: sessionsError } = await supabase.rpc('get_total_sessions_count');
        if (sessionsError) throw sessionsError;

        const { data: activeSessions, error: activeSessionsError } = await supabase.rpc('get_active_sessions_count');
        if (activeSessionsError) throw activeSessionsError;

        const { data: totalPlayers, error: playersError } = await supabase.rpc('get_total_players_count');
        if (playersError) throw playersError;

        setStats({
          totalUsers,
          totalSessions,
          activeSessions,
          totalPlayers,
          error: null,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        toast.error('Failed to load dashboard stats');
        setStats({
          totalUsers: 0,
          totalSessions: 0,
          activeSessions: 0,
          totalPlayers: 0,
          error: error.message,
          loading: false
        });
      }
    };

    fetchStats();
  }, []);

  const menuItems = [
    {
      title: 'User Management',
      description: 'Manage users and their roles',
      path: '/admin/manage?tab=users',
      state: 'users',
      icon: UsersIcon,
      stats: `${stats.totalUsers} Active Users`
    },
    {
      title: 'Session Management',
      description: 'Create and manage Pickleball sessions',
      path: '/admin/manage?tab=classes',
      state: 'classes',
      icon: CalendarIcon,
      stats: `${stats.activeSessions} Active Sessions`,
      onClick: () => {
        // You would need to fetch and set the selected session here
        setSelectedSession(/* session data */);
      }
    },
    {
      title: 'Analytics Dashboard',
      description: 'View detailed analytics and insights',
      path: '/admin/manage?tab=analytics',
      state: 'analytics',
      icon: ChartBarIcon,
      stats: `${stats.totalSessions} Total Sessions`
    },
    {
      title: 'Player Management',
      description: 'Manage player profiles and bookings',
      path: '/admin/manage?tab=players',
      state: 'players',
      icon: CurrencyDollarIcon,
      stats: `${stats.totalPlayers} Active Players`
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      path: '/admin/manage?tab=settings',
      state: 'settings',
      icon: CogIcon,
      stats: 'Manage Settings'
    }
  ];

  if (authLoading || stats.loading) {
    return (
      <DashboardContainer>
        <Header>
          <HeaderContent>
            <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          </HeaderContent>
        </Header>
        <LoadingContainer>
          <Spinner size="large" />
          <div className="loading-text">Loading Admin Dashboard...</div>
        </LoadingContainer>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <Header>
        <HeaderContent>
          <Logo src={logoHelderberg} alt="Pickleball Helderberg" />
          <UserInfo>
            <Welcome>Welcome, {getDisplayName(user)}</Welcome>
            <Role>Administrator</Role>
          </UserInfo>
        </HeaderContent>
      </Header>
      <Content>
        <TitleSection>
          <Title>Admin Dashboard</Title>
          <Subtitle>Manage your Pickleball community with powerful admin tools</Subtitle>
        </TitleSection>
        {stats.error ? (
          <ErrorContainer>
            <h3>Error Loading Dashboard</h3>
            <p>Error loading stats: {stats.error}</p>
          </ErrorContainer>
        ) : (
          <DashboardGrid>
            {menuItems.map((item) => (
              <Card key={item.state}>
                <MenuLink 
                  to={item.path}
                  state={{ activeTab: item.state }}
                >
                  <h2>
                    <item.icon />
                    {item.title}
                  </h2>
                  <p>{item.description}</p>
                  <Stats>{item.stats}</Stats>
                </MenuLink>
              </Card>
            ))}
          </DashboardGrid>
        )}
        {selectedSession && (
          <BookingsList sessionId={selectedSession.id} />
        )}
      </Content>
    </DashboardContainer>
  );
};

export default AdminDashboard;
