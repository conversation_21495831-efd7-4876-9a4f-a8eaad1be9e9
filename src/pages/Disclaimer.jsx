import React from 'react';
import styled from 'styled-components';
import { theme } from '../styles/theme';

const DisclaimerContainer = styled.div`
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  margin-top: 100px;
`;

const DisclaimerContent = styled.div`
  background: ${theme.colors.background};
  padding: 2rem;
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
`;

const Title = styled.h1`
  color: ${theme.colors.text};
  margin-bottom: 1.5rem;
`;

const Text = styled.p`
  color: ${theme.colors.textLight};
  line-height: 1.6;
`;

const Disclaimer = () => {
  return (
    <DisclaimerContainer>
      <DisclaimerContent>
        <Title>Disclaimer</Title>
        <Text>
          Pickleball Helderberg are not liable for any injuries, accidents, or damages 
          that occur during participation in pickleball activities. All participants 
          play at their own risk and are responsible for their personal safety and 
          belongings. Services rendered remain the property of Pickleball Helderberg. 
          By engaging in these activities, participants agree to these terms.
        </Text>
      </DisclaimerContent>
    </DisclaimerContainer>
  );
};

export default Disclaimer;