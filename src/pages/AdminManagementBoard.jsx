import React, { useState, useEffect } from 'react';
import styled from '@emotion/styled';
import AdminClassManagement from '../components/AdminClassManagement';
import UserRoleManagement from '../components/UserRoleManagement';
import { useIsAdmin } from '../hooks/useIsAdmin';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { theme } from '../styles/theme';
import { format, isAfter, isBefore, startOfWeek, addWeeks } from 'date-fns';

const AdminContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.background};
  padding: ${theme.spacing.md};
  padding-top: calc(${theme.spacing.xl} + 60px); /* Account for fixed header */
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
    padding-top: calc(${theme.spacing.xl} + 60px);
  }
`;

const Header = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: ${theme.colors.background};
  padding: ${theme.spacing.md};
  z-index: 10;
  box-shadow: ${theme.shadows.sm};
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg} ${theme.spacing.xl};
  }
`;

const Title = styled.h1`
  font-size: ${theme.fontSizes.xl};
  color: ${theme.colors.secondary};
  margin-bottom: 0;
  
  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes['3xl']};
  }
`;

const TabContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.xs};
  margin: ${theme.spacing.md} -${theme.spacing.md};
  padding: 0 ${theme.spacing.md};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    gap: ${theme.spacing.sm};
    margin: ${theme.spacing.xl} 0;
    padding: 0;
    border-bottom: 2px solid ${theme.colors.border};
  }
`;

const TabButton = styled.button`
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: none;
  border-radius: ${theme.borderRadius.full};
  background-color: ${props => props['data-active'] ? theme.colors.primary : theme.colors.background};
  color: ${props => props['data-active'] ? theme.colors.background : theme.colors.text};
  font-weight: ${props => props['data-active'] ? theme.fontWeights.bold : theme.fontWeights.medium};
  font-size: ${theme.fontSizes.sm};
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: ${props => props['data-active'] ? theme.shadows.md : 'none'};
  
  &:hover {
    background-color: ${props => props['data-active'] ? theme.colors.primary : theme.colors.border};
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.md} ${theme.spacing.lg};
    border-radius: ${theme.borderRadius.md} ${theme.borderRadius.md} 0 0;
    font-size: ${theme.fontSizes.md};
    box-shadow: none;

  }
`;

const ViewToggle = styled.div`
  display: flex;
  gap: ${theme.spacing.xs};
  margin-bottom: ${theme.spacing.md};
  padding: 0 ${theme.spacing.xs};
  
  @media (min-width: ${theme.breakpoints.md}) {
    gap: ${theme.spacing.sm};
    margin-bottom: ${theme.spacing.lg};
    padding: 0;
  }
`;

const ToggleButton = styled.button`
  flex: 1;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: 1px solid ${props => props['data-active'] ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.full};
  background-color: ${props => props['data-active'] ? theme.colors.primary + '10' : 'transparent'};
  color: ${props => props['data-active'] ? theme.colors.primary : theme.colors.text};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props['data-active'] ? theme.colors.primary + '20' : theme.colors.border + '40'};
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    flex: 0 1 auto;
    font-size: ${theme.fontSizes.md};
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${theme.spacing.md};
  
  @media (min-width: ${theme.breakpoints.sm}) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    grid-template-columns: repeat(3, 1fr);
    gap: ${theme.spacing.lg};
  }
`;

const StatCard = styled.div`
  background-color: ${theme.colors.background};
  padding: ${theme.spacing.md};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }
`;

const StatTitle = styled.h3`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.text};
  margin-bottom: ${theme.spacing.xs};
  
  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.md};
    margin-bottom: ${theme.spacing.sm};
  }
`;

const StatValue = styled.span`
  font-size: ${theme.fontSizes.lg};
  color: ${props => props.$positive ? theme.colors.success : theme.colors.error};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.xs};
  
  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.xl};
    margin-bottom: ${theme.spacing.sm};
  }
`;

const StatChange = styled.p`
  font-size: ${theme.fontSizes.xs};
  color: ${props => props.positive ? theme.colors.success : theme.colors.error};
  
  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.sm};
  }
`;

const RevenueStats = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${theme.spacing.md};
  
  @media (min-width: ${theme.breakpoints.sm}) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${theme.spacing.lg};
  }
`;

const SettingsContainer = styled.div`
  padding: ${theme.spacing.md};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
  
  h2 {
    font-size: ${theme.fontSizes.xl};
    color: ${theme.colors.secondary};
    margin-bottom: ${theme.spacing.lg};
    
    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes['2xl']};
    }
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }
`;

const SettingsForm = styled.form`
  margin-top: ${theme.spacing.lg};
  max-width: 600px;
`;

const SettingsGroup = styled.div`
  background: ${theme.colors.background};
  padding: ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  border: 1px solid ${theme.colors.border};
  margin-bottom: ${theme.spacing.lg};
  
  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }
`;

const SettingsTitle = styled.h3`
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  margin-bottom: ${theme.spacing.md};
  
  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.lg};
  }
`;

const SettingItem = styled.div`
  margin-bottom: ${theme.spacing.md};
  
  label {
    display: block;
    font-size: ${theme.fontSizes.sm};
    color: ${theme.colors.text};
    margin-bottom: ${theme.spacing.xs};
    
    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes.md};
    }
  }
  
  input[type="number"],
  input[type="text"] {
    width: 100%;
    padding: ${theme.spacing.sm};
    border: 1px solid ${theme.colors.border};
    border-radius: ${theme.borderRadius.sm};
    font-size: ${theme.fontSizes.md};
    
    &:focus {
      outline: none;
      border-color: ${theme.colors.primary};
      box-shadow: 0 0 0 2px ${theme.colors.primary}20;
    }
  }
  
  input[type="checkbox"] {
    margin-right: ${theme.spacing.xs};
    width: 20px;
    height: 20px;
  }
`;

const SaveButton = styled.button`
  width: 100%;
  padding: ${theme.spacing.md};
  border: none;
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.primary};
  color: white;
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.bold};
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: ${theme.spacing.xl};
  
  &:hover {
    background-color: ${theme.colors.buttonHover.primary};
  }
  
  @media (min-width: ${theme.breakpoints.md}) {
    width: auto;
    padding: ${theme.spacing.md} ${theme.spacing.xl};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
`;

const AdminManagementBoard = () => {
  const [activeTab, setActiveTab] = useState('classes');
  const [classesView, setClassesView] = useState('upcoming');
  const [settings, setSettings] = useState({
    maxBookingsPerUser: 3,
    advanceBookingDays: 14,
    emailNotifications: true,
    smsNotifications: false,
    reminderHours: 24,
    cancellationHours: 12
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [stats, setStats] = useState({
    bookings: { total: 0, change: 0 },
    users: { active: 0, change: 0 },
    attendance: { rate: 0, change: 0 },
    revenue: { total: 0, average: 0, change: 0 }
  });
  const location = useLocation();
  const navigate = useNavigate();

  // Get the initial tab from URL search params
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab && ['classes', 'users', 'analytics', 'revenue', 'settings'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [location.search]);

  // Load stats
  useEffect(() => {
    const loadStats = async () => {
      setIsLoadingStats(true);
      try {
        // TODO: Replace with actual API call
        const mockStats = {
          bookings: { total: 100, change: 12 },
          users: { active: 50, change: 5 },
          attendance: { rate: 80, change: 0 },
          revenue: { total: 1000, average: 10, change: 15 }
        };
        setStats(mockStats);
      } catch (error) {
        console.error('Error loading stats:', error);
        // TODO: Show error toast
      } finally {
        setIsLoadingStats(false);
      }
    };

    if (activeTab === 'analytics' || activeTab === 'revenue') {
      loadStats();
    }
  }, [activeTab]);

  // Update URL when tab changes
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('tab', tab);
    navigate({ search: searchParams.toString() }, { replace: true });
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSettingsSave = async (e) => {
    e.preventDefault();
    try {
      // TODO: Replace with actual API call
      // toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      // toast.error('Failed to save settings');
    }
  };

  return (
    <AdminContainer>
      <Header>
        <Title>Admin Management Board</Title>
      </Header>

      <TabContainer>
        <TabButton
          onClick={() => handleTabChange('classes')}
          data-active={activeTab === 'classes'}
        >
          Classes
        </TabButton>
        <TabButton
          onClick={() => handleTabChange('users')}
          data-active={activeTab === 'users'}
        >
          Users
        </TabButton>
        <TabButton
          onClick={() => handleTabChange('analytics')}
          data-active={activeTab === 'analytics'}
        >
          Analytics
        </TabButton>
        <TabButton
          onClick={() => handleTabChange('revenue')}
          data-active={activeTab === 'revenue'}
        >
          Revenue
        </TabButton>
        <TabButton
          onClick={() => handleTabChange('settings')}
          data-active={activeTab === 'settings'}
        >
          Settings
        </TabButton>
      </TabContainer>

      {activeTab === 'classes' && (
        <div>
          <ViewToggle>
            <ToggleButton
              onClick={() => setClassesView('upcoming')}
              data-active={classesView === 'upcoming'}
            >
              Upcoming
            </ToggleButton>
            <ToggleButton
              onClick={() => setClassesView('past')}
              data-active={classesView === 'past'}
            >
              Past
            </ToggleButton>
            <ToggleButton
              onClick={() => setClassesView('all')}
              data-active={classesView === 'all'}
            >
              All Classes
            </ToggleButton>
          </ViewToggle>
          <AdminClassManagement view={classesView} />
        </div>
      )}

      {activeTab === 'users' && <UserRoleManagement />}
      
      {activeTab === 'analytics' && (
        <div>
          <h2>Analytics Dashboard</h2>
          {isLoadingStats ? (
            <LoadingContainer>Loading statistics...</LoadingContainer>
          ) : (
            <StatsGrid>
              <StatCard>
                <StatTitle>Total Bookings</StatTitle>
                <StatValue $positive={stats.bookings.change > 0}>{stats.bookings.total}</StatValue>
                <StatChange positive={stats.bookings.change > 0}>
                  {stats.bookings.change > 0 ? '+' : ''}{stats.bookings.change}% from last month
                </StatChange>
              </StatCard>
              <StatCard>
                <StatTitle>Active Users</StatTitle>
                <StatValue $positive={stats.users.change > 0}>{stats.users.active}</StatValue>
                <StatChange positive={stats.users.change > 0}>
                  {stats.users.change > 0 ? '+' : ''}{stats.users.change}% from last month
                </StatChange>
              </StatCard>
              <StatCard>
                <StatTitle>Class Attendance</StatTitle>
                <StatValue $positive={stats.attendance.change > 0}>{stats.attendance.rate}%</StatValue>
                <StatChange positive={stats.attendance.change > 0}>
                  {stats.attendance.change === 0 
                    ? 'No change from last month'
                    : `${stats.attendance.change > 0 ? '+' : ''}${stats.attendance.change}% from last month`
                  }
                </StatChange>
              </StatCard>
            </StatsGrid>
          )}
        </div>
      )}

      {activeTab === 'revenue' && (
        <div>
          <h2>Revenue Overview</h2>
          {isLoadingStats ? (
            <LoadingContainer>Loading revenue data...</LoadingContainer>
          ) : (
            <RevenueStats>
              <StatCard>
                <StatTitle>Total Revenue</StatTitle>
                <StatValue $positive={stats.revenue.change > 0}>R{stats.revenue.total}</StatValue>
                <StatChange positive={stats.revenue.change > 0}>
                  {stats.revenue.change > 0 ? '+' : ''}{stats.revenue.change}% from last month
                </StatChange>
              </StatCard>
              <StatCard>
                <StatTitle>Average per Booking</StatTitle>
                <StatValue $positive={stats.revenue.change > 0}>R{stats.revenue.average}</StatValue>
                <StatChange positive={stats.revenue.change > 0}>
                  {stats.revenue.change > 0 ? '+' : ''}{stats.revenue.change}% from last month
                </StatChange>
              </StatCard>
            </RevenueStats>
          )}
        </div>
      )}

      {activeTab === 'settings' && (
        <SettingsContainer>
          <h2>System Settings</h2>
          <SettingsForm onSubmit={handleSettingsSave}>
            <SettingsGroup>
              <SettingsTitle>Booking Settings</SettingsTitle>
              <SettingItem>
                <label>Maximum Bookings per User</label>
                <input 
                  type="number" 
                  value={settings.maxBookingsPerUser}
                  onChange={(e) => handleSettingChange('maxBookingsPerUser', parseInt(e.target.value, 10))}
                  min={1}
                  max={10}
                />
              </SettingItem>
              <SettingItem>
                <label>Advance Booking Days</label>
                <input 
                  type="number" 
                  value={settings.advanceBookingDays}
                  onChange={(e) => handleSettingChange('advanceBookingDays', parseInt(e.target.value, 10))}
                  min={1}
                  max={30}
                />
              </SettingItem>
            </SettingsGroup>
            <SettingsGroup>
              <SettingsTitle>Notification Settings</SettingsTitle>
              <SettingItem>
                <label>
                  <input 
                    type="checkbox" 
                    checked={settings.emailNotifications}
                    onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                  />
                  Email Notifications
                </label>
              </SettingItem>
              <SettingItem>
                <label>
                  <input 
                    type="checkbox" 
                    checked={settings.smsNotifications}
                    onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
                  />
                  SMS Notifications
                </label>
              </SettingItem>
              <SettingItem>
                <label>Reminder Hours Before Class</label>
                <input 
                  type="number" 
                  value={settings.reminderHours}
                  onChange={(e) => handleSettingChange('reminderHours', parseInt(e.target.value, 10))}
                  min={1}
                  max={72}
                />
              </SettingItem>
              <SettingItem>
                <label>Cancellation Hours Before Class</label>
                <input 
                  type="number" 
                  value={settings.cancellationHours}
                  onChange={(e) => handleSettingChange('cancellationHours', parseInt(e.target.value, 10))}
                  min={1}
                  max={48}
                />
              </SettingItem>
            </SettingsGroup>
            <SaveButton type="submit">Save Settings</SaveButton>
          </SettingsForm>
        </SettingsContainer>
      )}
    </AdminContainer>
  );
};

export default AdminManagementBoard;
