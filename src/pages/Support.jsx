import styled from 'styled-components';
import { theme } from '../styles/theme';

const Card = styled.div`
  background: #fffbe6;
  border: 1px solid ${theme.colors.warning};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.lg};
  max-width: 500px;
  margin: 40px auto;
`;

const Message = styled.p`
  color: ${theme.colors.warning};
  font-size: ${theme.fontSizes.md};
  font-weight: 500;
  text-align: center;
`;

const Container = styled.div`
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${theme.colors.background};
  padding: ${theme.spacing.lg};
`;

export default function Support() {
  return (
    <Container>
      <Card>
        <Message>
          <span role="img" aria-label="help">❓</span> <b>Having trouble with signup or payment?</b><br />
          If you have paid but did not receive your account, please contact us at <a href="mailto:<EMAIL>" style={{ color: theme.colors.primary, textDecoration: 'underline' }}>pickleball<PERSON><PERSON><PERSON>@gmail.com</a>.<br />
          Include your payment reference and email address so we can assist you quickly.
        </Message>
      </Card>
    </Container>
  );
} 