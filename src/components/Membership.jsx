import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import styled from 'styled-components';
import { theme } from '../styles/theme';
import { useMembership } from '../hooks/useMembership';

const Container = styled.div`
  padding: ${theme.spacing.xl};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  margin: ${theme.spacing.xl} 0;
`;

const Section = styled.div`
  margin-bottom: ${theme.spacing.xl};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h2`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.xl};
  margin-bottom: ${theme.spacing.lg};
`;

const Card = styled.div`
  background: ${theme.colors.background};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.lg};
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  background: ${props => props.$active ? theme.colors.success : theme.colors.textLight};
  color: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.sm};
  margin-bottom: ${theme.spacing.md};
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.sm};

  &:last-child {
    margin-bottom: 0;
  }
`;

const Label = styled.span`
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.sm};
`;

const Value = styled.span`
  color: ${theme.colors.text};
  font-weight: ${theme.fontWeights.medium};
`;

const PlansGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${theme.spacing.lg};
`;

const PlanCard = styled.div`
  background: ${theme.colors.background};
  border: 2px solid ${props => props.$selected ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.lg};
  display: flex;
  flex-direction: column;
  opacity: ${props => props.readOnly && !props.$selected ? 0.6 : 1};
  position: relative;

  ${props => props.$selected && `
    &::after {
      content: '✓ Selected';
      position: absolute;
      top: ${theme.spacing.sm};
      right: ${theme.spacing.sm};
      background: ${theme.colors.primary};
      color: white;
      padding: ${theme.spacing.xs} ${theme.spacing.sm};
      border-radius: ${theme.borderRadius.sm};
      font-size: ${theme.fontSizes.sm};
    }
  `}
`;

const PlanTitle = styled.h3`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.lg};
  margin-bottom: ${theme.spacing.md};
`;

const Price = styled.div`
  color: ${theme.colors.primary};
  font-size: ${theme.fontSizes['2xl']};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.md};
`;

const PriceDetail = styled.span`
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.normal};
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: ${theme.spacing.md} 0;
  flex-grow: 1;
`;

const Feature = styled.li`
  color: ${theme.colors.text};
  margin-bottom: ${theme.spacing.sm};
  display: flex;
  align-items: center;

  &:before {
    content: "✓";
    color: ${theme.colors.success};
    margin-right: ${theme.spacing.sm};
  }
`;

const SelectButton = styled.button`
  background: ${theme.colors.primary};
  color: ${theme.colors.background};
  border: none;
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.medium};
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;

  &:hover {
    background: ${theme.colors.buttonHover.primary};
  }

  &:disabled {
    background: ${theme.colors.border};
    cursor: not-allowed;
  }
`;

const Message = styled.p`
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.sm};
  text-align: center;
`;

// Export plans array for reuse
export const membershipPlans = [
  {
    id: 'loyalty-card',
    title: 'Loyalty Card',
    price: 'R400',
    amountInCents: 40000,
    period: '5 sessions + 1 FREE session',
    features: [
      'Pay for 5 sessions, get your 6th FREE',
      'Online booking system',
      'Valid for 6 months',
      'Best value for occasional players'
    ],
    allowedSchools: ['SWTC']
  },
  {
    id: 'once-off',
    title: 'Once-off Session',
    price: 'R80',
    amountInCents: 8000,
    period: '1 session',
    features: [
      'Single session access',
      'Online booking system',
      'Valid for 24 hours',
      'Perfect for trying out'
    ],
    allowedSchools: ['SWTC']
  },
  {
    id: 'quarterly',
    title: 'Quarterly Individual',
    price: 'R675',
    amountInCents: 67500,
    period: '4 months',
    features: [
      'Access to all classes',
      'Online booking system',
      'Quarterly payment option'
    ],
    allowedSchools: ['Curro Sitari', 'Hendrik Louw Primary', 'Lochnerhof Primary', 'Generation Somerset West', 'Somerset College']
  },
  {
    id: 'annual',
    title: 'Annual Individual',
    price: 'R2,400',
    amountInCents: 240000,
    period: '12 months',
    features: [
      'Access to all classes',
      'Online booking system',
      'Free shirt and cap',
      'Best value option'
    ],
    allowedSchools: ['Curro Sitari', 'Hendrik Louw Primary', 'Lochnerhof Primary', 'Generation Somerset West', 'Somerset College']
  },
  {
    id: 'quarterly-family',
    title: 'Quarterly Family',
    price: 'R625',
    amountInCents: 62500,
    period: 'per person / 4 months',
    features: [
      'Family discount rate',
      'Access to all classes',
      'Online booking system',
      'Manage multiple family members'
    ],
    allowedSchools: ['Curro Sitari', 'Hendrik Louw Primary', 'Lochnerhof Primary', 'Generation Somerset West', 'Somerset College']
  },
  {
    id: 'annual-family',
    title: 'Annual Family',
    price: 'R2,200',
    amountInCents: 220000,
    period: 'per person / 12 months',
    features: [
      'Family discount rate',
      'Access to all classes',
      'Online booking system',
      'Free shirt and cap',
      'Best value option',
      'Manage multiple family members'
    ],
    allowedSchools: ['Curro Sitari', 'Hendrik Louw Primary', 'Lochnerhof Primary', 'Generation Somerset West', 'Somerset College']
  }
];

const Membership = ({
  isSignupFlow = false,
  onPlanSelect = null,
  readOnly = false,
  selectedPlan = null
}) => {
  const [loadingPlanId, setLoadingPlanId] = useState(null);
  const trialLessonsUsed = 0;
  const trialLessonsTotal = 2;
  const trialActive = true;
  const { refreshMembership } = useMembership();
  const [userSchool, setUserSchool] = useState('');

  useEffect(() => {
    // Get user's school from session storage
    const storedDetails = sessionStorage.getItem('userDetails');
    if (storedDetails) {
      const details = JSON.parse(storedDetails);
      setUserSchool(details.school);
    }
  }, []);

  // Filter plans based on user's school
  const filteredPlans = membershipPlans.filter(plan => 
    plan.allowedSchools.includes(userSchool)
  );

  // useEffect(() => {
  //   if (!window.PaymentRequest) {
  //     toast.error('Your browser does not support the Payment Request API');
  //   }
  // }, []);

  const handleSelectPlan = async (plan) => {
    try {
      setLoadingPlanId(plan.id);
      if (onPlanSelect) {
        // Call the onPlanSelect handler (which initiates payment)
        await onPlanSelect(plan);

        // Note: The actual payment completion and data refresh happens after
        // the user is redirected back from PayFast. At that point, the
        // SignupStepper component handles creating the payment record.
        //
        // The useMembership hook will fetch the updated data when the user
        // returns to the dashboard after payment completion.

        // Set flags in sessionStorage to indicate this is a renewal
        // and that we should refresh membership data when the user returns
        sessionStorage.setItem('refreshMembership', 'true');
        sessionStorage.setItem('isRenewal', 'true');

        // If we're not in the signup flow, we can refresh immediately
        if (!isSignupFlow) {
          refreshMembership();
        }
      } else {
        console.error('No plan selection handler provided');
        toast.error('Unable to process plan selection');
      }
    } catch (error) {
      console.error('Error selecting plan:', error);
      toast.error(error.message || 'Failed to select plan. Please try again.');
    } finally {
      setLoadingPlanId(null); // Clear loading state
    }
  };

  return (
    <Container>
      {!isSignupFlow && (
        <Section>
          <SectionTitle>Free Trial Status</SectionTitle>
          <Card>
            <StatusBadge $active={trialActive}>Trial Active</StatusBadge>
            <InfoRow>
              <Label>Lessons Used</Label>
              <Value>{trialLessonsUsed} of {trialLessonsTotal}</Value>
            </InfoRow>
          </Card>
        </Section>
      )}

      <Section>
        <SectionTitle>
          {isSignupFlow ? 'Choose Your Plan' : 'Membership Plans'}
        </SectionTitle>
        {filteredPlans.length > 0 ? (
          <PlansGrid>
            {filteredPlans.map((plan) => (
              <PlanCard key={plan.id} $selected={selectedPlan?.id === plan.id} readOnly={readOnly}>
                <PlanTitle>{plan.title}</PlanTitle>
                <Price>
                  {plan.price} <PriceDetail>{plan.period}</PriceDetail>
                </Price>
                <FeaturesList>
                  {plan.features.map((feature, index) => (
                    <Feature key={index}>{feature}</Feature>
                  ))}
                </FeaturesList>
                <SelectButton
                  onClick={() => handleSelectPlan(plan)}
                  disabled={loadingPlanId !== null || readOnly}
                >
                  {loadingPlanId === plan.id ? 'Processing...' :
                    readOnly ? 'Selected' :
                    isSignupFlow ? 'Choose Plan' : 'Select Plan'}
                </SelectButton>
              </PlanCard>
            ))}
          </PlansGrid>
        ) : (
          <Card>
            <Message>No plans available for your selected school/club. Please contact support if you believe this is an error.</Message>
          </Card>
        )}
      </Section>
    </Container>
  );
};

Membership.propTypes = {
  isSignupFlow: PropTypes.bool,
  onPlanSelect: PropTypes.func,
  readOnly: PropTypes.bool,
  selectedPlan: PropTypes.object
};

export default Membership;
