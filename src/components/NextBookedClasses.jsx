import { useMemo } from 'react';
import styled from 'styled-components';
import { theme } from '../styles/theme';
import { format, parseISO, isFuture, isToday, isTomorrow, differenceInHours } from 'date-fns';
import { Link } from 'react-router-dom';
import { useBookings } from '../hooks/useBookingsQuery';
import { useAuth } from '../hooks/useAuth';
import Spinner from './Spinner';

const Container = styled.div`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.lg};
  box-shadow: ${theme.shadows.sm};
`;

const SectionTitle = styled.h2`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.lg};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const BookNowLink = styled(Link)`
  color: ${theme.colors.primary};
  text-decoration: none;
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.medium};
  
  &:hover {
    text-decoration: underline;
  }
`;

const ClassCard = styled.div`
  background: ${theme.colors.background};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.md};
  transition: all 0.2s ease;
  
  &:hover {
    border-color: ${theme.colors.primary};
    box-shadow: ${theme.shadows.sm};
  }
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const ClassHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${theme.spacing.sm};
`;

const ClassTitle = styled.h3`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.semibold};
  margin: 0;
`;

const DateBadge = styled.span`
  background: ${props => {
    if (isToday(parseISO(props.$date))) return theme.colors.success + '20';
    if (isTomorrow(parseISO(props.$date))) return theme.colors.warning + '20';
    return theme.colors.primary + '20';
  }};
  color: ${props => {
    if (isToday(parseISO(props.$date))) return theme.colors.success;
    if (isTomorrow(parseISO(props.$date))) return theme.colors.warning;
    return theme.colors.primary;
  }};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.fontSizes.xs};
  font-weight: ${theme.fontWeights.medium};
  text-transform: uppercase;
`;

const ClassDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const DetailRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.sm};
  
  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xl};
  color: ${theme.colors.textLight};
  
  h3 {
    color: ${theme.colors.text};
    margin-bottom: ${theme.spacing.sm};
    font-size: ${theme.fontSizes.lg};
  }
  
  p {
    margin-bottom: ${theme.spacing.lg};
    font-size: ${theme.fontSizes.md};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${theme.spacing.xl};
`;

const NextBookedClasses = () => {
  const { user } = useAuth();
  const { data: bookings, isLoading } = useBookings(user?.id);

  const nextClasses = useMemo(() => {
    if (!bookings) return [];

    return bookings
      .filter(booking => {
        // Only include bookings with valid sessions that are in the future
        return booking.session && 
               booking.session.start_time && 
               isFuture(parseISO(booking.session.start_time)) &&
               booking.status === 'confirmed';
      })
      .sort((a, b) => {
        // Sort by start time, earliest first
        return new Date(a.session.start_time) - new Date(b.session.start_time);
      })
      .slice(0, 3); // Show only next 3 classes
  }, [bookings]);

  const getDateLabel = (dateString) => {
    const date = parseISO(dateString);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    return format(date, 'MMM d');
  };

  if (isLoading) {
    return (
      <Container>
        <SectionTitle>Your Next Classes</SectionTitle>
        <LoadingContainer>
          <Spinner />
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container>
      <SectionTitle>
        Your Next Classes
        <BookNowLink to="/booking">Book More</BookNowLink>
      </SectionTitle>
      
      {nextClasses.length === 0 ? (
        <EmptyState>
          <h3>No upcoming classes</h3>
          <p>You don't have any classes booked yet.</p>
          <BookNowLink to="/booking">Book your first class</BookNowLink>
        </EmptyState>
      ) : (
        nextClasses.map((booking) => (
          <ClassCard key={booking.id}>
            <ClassHeader>
              <ClassTitle>{booking.session.title}</ClassTitle>
              <DateBadge $date={booking.session.start_time}>
                {getDateLabel(booking.session.start_time)}
              </DateBadge>
            </ClassHeader>
            
            <ClassDetails>
              <DetailRow>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M12 6v6l4 2"/>
                </svg>
                <span>
                  {format(parseISO(booking.session.start_time), 'EEEE, MMMM d')} • {' '}
                  {format(parseISO(booking.session.start_time), 'h:mm a')} - {' '}
                  {format(parseISO(booking.session.end_time), 'h:mm a')}
                </span>
              </DetailRow>
              
              <DetailRow>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
                <span>{booking.session.school}</span>
              </DetailRow>

              {booking.session.coach_id && (
                <DetailRow>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                  </svg>
                  <span>Coach assigned</span>
                </DetailRow>
              )}

              {(() => {
                const sessionStartTime = parseISO(booking.session.start_time);
                const now = new Date();
                const hoursUntilSession = differenceInHours(sessionStartTime, now);

                if (hoursUntilSession <= 1) {
                  return (
                    <DetailRow style={{ color: theme.colors.primary }}>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                      </svg>
                      <span>You're all set! See you there</span>
                    </DetailRow>
                  );
                }
                return null;
              })()}
            </ClassDetails>
          </ClassCard>
        ))
      )}
    </Container>
  );
};

export default NextBookedClasses;
