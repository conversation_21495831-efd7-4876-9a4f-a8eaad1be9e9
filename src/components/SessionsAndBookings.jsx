import { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { useAuth } from '../hooks/useAuth';
import { useBookings, useCreateBooking, useCancelBooking } from '../hooks/useBookingsQuery';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { toast } from 'react-hot-toast';
import Spinner from './Spinner';
import Modal from './Modal';
import { format, parseISO, isBefore } from 'date-fns';
import { theme } from '../styles/theme';
import { Link } from 'react-router-dom';
import { useProfile } from '../hooks/useProfiles';
import { useMembership } from '../hooks/useMembership'; // Added import
import { canCancelBooking } from '../utils/bookingUtils';

const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xl};
  min-height: 200px;
  justify-content: ${props => props.isLoading ? 'center' : 'flex-start'};
  align-items: ${props => props.isLoading ? 'center' : 'stretch'};
  padding: ${theme.spacing.md};
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: ${theme.borderRadius.lg};
  box-sizing: border-box;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const NoSessions = styled.div`
  text-align: center;
  padding: ${theme.spacing['4xl']};
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 600px;
  margin: 0 auto;

  &::before {
    content: '📅';
    display: block;
    font-size: ${theme.fontSizes.xxl};
    margin-bottom: ${theme.spacing.lg};
  }

  h3 {
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.xl};
    margin: 0 0 ${theme.spacing.md};
    font-weight: ${theme.fontWeights.bold};
  }

  p {
    margin: 0 0 ${theme.spacing.lg};
    font-size: ${theme.fontSizes.md};
    color: ${theme.colors.textLight};
  }

  a {
    display: inline-flex;
    align-items: center;
    gap: ${theme.spacing.sm};
    margin-top: ${theme.spacing.lg};
    padding: ${theme.spacing.md} ${theme.spacing.lg};
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    color: white;
    text-decoration: none;
    font-weight: ${theme.fontWeights.semibold};
    border-radius: ${theme.borderRadius.lg};
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    &::before {
      content: '🎾';
      font-size: ${theme.fontSizes.md};
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      text-decoration: none;
    }
  }
`;

const DateGroup = styled.div`
  width: 100%;
  margin-bottom: ${theme.spacing.lg};

  &:last-child {
    margin-bottom: 0;
  }
`;

const DateHeader = styled.h2`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.primary};
  margin-bottom: ${theme.spacing.xl};
  padding: ${theme.spacing.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${theme.spacing.md};
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  &::before {
    content: '⏰';
    font-size: ${theme.fontSizes.lg};
    flex-shrink: 0;
  }

  span {
    font-size: ${theme.fontSizes.md};
    color: ${theme.colors.textLight};
    font-weight: ${theme.fontWeights.semibold};
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    color: white;
    padding: ${theme.spacing.xs} ${theme.spacing.sm};
    border-radius: ${theme.borderRadius.full};
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: ${theme.spacing.xs};

    &::before {
      font-size: ${theme.fontSizes.sm};
    }
  }
`;

const SessionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${theme.spacing.lg};
  width: 100%;

  @media (min-width: ${theme.breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
`;

const SessionCard = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: ${theme.spacing.xl};
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  opacity: ${props => props.$isPast ? 0.7 : 1};
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};

  &:hover {
    transform: ${props => props.$isPast ? 'none' : 'translateY(-4px)'};
    box-shadow: ${props => props.$isPast ? '0 8px 32px rgba(0, 0, 0, 0.1)' : '0 12px 40px rgba(0, 0, 0, 0.15)'};
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => {
      if (props.$isBooked) return `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary})`;
      if (props.$isFull) return `linear-gradient(90deg, ${theme.colors.error}, #dc2626)`;
      return `linear-gradient(90deg, ${theme.colors.success}, #059669)`;
    }};
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: ${props => {
      if (props.$isBooked) return theme.colors.primary;
      if (props.$isFull) return theme.colors.error;
      return theme.colors.success;
    }};
    opacity: 0.8;
  }
`;

const SessionTime = styled.div`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.textLight};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  padding: ${theme.spacing.sm};
  background: rgba(248, 250, 252, 0.5);
  border-radius: ${theme.borderRadius.sm};
  border-bottom: 1px solid ${theme.colors.border}20;

  svg {
    width: 18px;
    height: 18px;
    color: ${theme.colors.primary};
    flex-shrink: 0;
  }

  span {
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};
    font-weight: ${theme.fontWeights.medium};
  }
`;

const SessionTitle = styled.h3`
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${theme.spacing.sm};
  line-height: 1.4;
  margin: 0;

  &::before {
    content: '🎾';
    font-size: ${theme.fontSizes.md};
    margin-right: ${theme.spacing.xs};
    flex-shrink: 0;
  }
`;

const SessionDetails = styled.div`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.textLight};
  display: grid;
  gap: ${theme.spacing.md};

  .info-row {
    display: flex;
    align-items: flex-start;
    gap: ${theme.spacing.sm};
    padding: ${theme.spacing.sm};
    background: rgba(248, 250, 252, 0.5);
    border-radius: ${theme.borderRadius.sm};
    transition: all 0.2s ease;

    &:hover {
      background: rgba(248, 250, 252, 0.8);
      transform: translateX(4px);
    }

    svg {
      width: 18px;
      height: 18px;
      color: ${theme.colors.primary};
      flex-shrink: 0;
      margin-top: 2px;
    }

    .label {
      font-weight: ${theme.fontWeights.semibold};
      color: ${theme.colors.text};
      min-width: 60px;
    }

    .value {
      color: ${theme.colors.textLight};
      font-weight: ${theme.fontWeights.medium};
    }
  }
`;

const Button = styled.button`
  padding: ${theme.spacing.sm} ${theme.spacing.lg};
  background: ${props => props.$secondary
    ? 'transparent'
    : `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`};
  color: ${props => props.$secondary ? theme.colors.primary : 'white'};
  border: 2px solid ${props => props.$secondary ? theme.colors.primary : 'transparent'};
  border-radius: ${theme.borderRadius.lg};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.5 : 1};
  font-weight: ${theme.fontWeights.semibold};
  font-size: ${theme.fontSizes.sm};
  transition: all 0.3s ease;
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.xs};
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    background: ${props => props.disabled ?
      props.$secondary ? 'transparent' : `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`
      : props.$secondary ? theme.colors.primary + '10' : `linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.primary})`};
    transform: ${props => props.disabled ? 'none' : 'translateY(-2px)'};
    box-shadow: ${props => props.disabled ? '0 4px 15px rgba(0, 0, 0, 0.1)' : '0 8px 25px rgba(0, 0, 0, 0.15)'};
  }

  &:active {
    transform: translateY(0);
  }

  &.book-now::after {
    content: '🎾';
    margin-left: ${theme.spacing.xs};
  }

  &.cancel::after {
    content: '❌';
    margin-left: ${theme.spacing.xs};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
  margin-top: auto;
  padding-top: ${theme.spacing.lg};
  border-top: 1px solid rgba(0, 0, 0, 0.1);
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  background: ${props => {
    switch (props.$type) {
      case 'full': return `linear-gradient(135deg, ${theme.colors.error}15, ${theme.colors.error}25)`;
      case 'booked': return `linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.primary}25)`;
      default: return `linear-gradient(135deg, ${theme.colors.success}15, ${theme.colors.success}25)`;
    }
  }};
  color: ${props => {
    switch (props.$type) {
      case 'full': return theme.colors.error;
      case 'booked': return theme.colors.primary;
      default: return theme.colors.success;
    }
  }};
  border: 1px solid ${props => {
    switch (props.$type) {
      case 'full': return theme.colors.error + '30';
      case 'booked': return theme.colors.primary + '30';
      default: return theme.colors.success + '30';
    }
  }};
  border-radius: ${theme.borderRadius.full};
  font-size: ${theme.fontSizes.xs};
  font-weight: ${theme.fontWeights.bold};
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &::before {
    content: ${props => {
      switch (props.$type) {
        case 'full': return "'🚫'";
        case 'booked': return "'✅'";
        default: return "'🟢'";
      }
    }};
    font-size: ${theme.fontSizes.xs};
  }
`;

const ParticipantsList = styled.div`
  margin-top: ${theme.spacing.sm};
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
  background: rgba(248, 250, 252, 0.3);
  border-radius: ${theme.borderRadius.sm};
  padding: ${theme.spacing.sm};
  border: 1px solid rgba(0, 0, 0, 0.05);
`;

const ParticipantsHeader = styled.div`
  color: ${theme.colors.primary};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.bold};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  margin-bottom: ${theme.spacing.xs};

  &::before {
    content: '👥';
    font-size: ${theme.fontSizes.sm};
  }
`;

const ParticipantsNames = styled.div`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.sm};
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
  margin-top: ${theme.spacing.xs};
`;

const ParticipantRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: ${theme.spacing.sm};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  background: rgba(255, 255, 255, 0.5);
  border-radius: ${theme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(4px);
  }

  svg {
    min-width: 20px !important;
    min-height: 20px !important;
    width: 20px !important;
    height: 20px !important;
    color: ${theme.colors.primary};
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  > span {
    display: inline-flex;
    align-items: center;
    font-size: ${theme.fontSizes.sm};
    font-weight: ${theme.fontWeights.medium};
    color: ${theme.colors.text};
  }
`;

const CancelModal = ({ onConfirm, onCancel, isLoading }) => (
  <Modal
    isOpen={true}
    onClose={onCancel}
    title="Cancel Booking"
    onConfirm={onConfirm}
    confirmText="Yes, Cancel"
    cancelText="No, Keep"
    loading={isLoading}
  >
    <div>
      <p>Are you sure you want to cancel this booking?</p>
    </div>
  </Modal>
);

CancelModal.propTypes = {
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  isLoading: PropTypes.bool
};

const SessionsAndBookings = ({ selectedDate, sessions = [], isLoadingProp = false }) => {
  const { user } = useAuth();
  const { data: bookings, isLoading: isLoadingBookings } = useBookings(user?.id);
  const createBookingMutation = useCreateBooking();
  const cancelBookingMutation = useCancelBooking();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [isCancelling, setIsCancelling] = useState(false);
  const [bookingInProgress, setBookingInProgress] = useState(null);
  const { isLoading: isLoadingProfile } = useProfile(user?.id);
  const queryClient = useQueryClient();
  const { membershipDetails, isLoading: isLoadingMembership } = useMembership(user?.id); // Fetch membership details
  const [participantProfiles, setParticipantProfiles] = useState({});
  // Create a map of session IDs to their correct confirmed booking counts
  const [sessionBookingCounts, setSessionBookingCounts] = useState({});
  // We no longer need the refresh counter since we're using React Query

  // Define targetSessionId at the top level for use in hooks
  const targetSessionId = '0757c459-3bc5-4249-8b2a-c3b3a258aa59';

  // Direct database query for the target session and all other sessions
  useEffect(() => {
    const fetchAllSessionsData = async () => {
      try {
        // Get all session IDs from the current sessions prop
        if (!sessions || sessions.length === 0) {
          console.log('No sessions to fetch booking counts for');
          return;
        }

        const sessionIds = sessions.map(s => s.id);
        console.log('Fetching booking counts for session IDs:', sessionIds);

        // Get all bookings for these sessions in a single query with user details
        const { data: allBookings, error: allBookingsError } = await supabase
          .from('bookings_with_users')
          .select('*')
          .in('session_id', sessionIds);

        if (allBookingsError) {
          console.error('Error fetching all bookings:', allBookingsError);
          return;
        }

        // Log the bookings for the target session
        if (sessionIds.includes(targetSessionId)) {
          const targetBookings = allBookings.filter(b => b.session_id === targetSessionId);
          console.log('TARGET SESSION BOOKINGS FROM QUERY:', targetBookings);

          const confirmedTargetBookings = targetBookings.filter(b => b.status === 'confirmed');
          console.log('CONFIRMED TARGET SESSION BOOKINGS FROM QUERY:', confirmedTargetBookings);

          // Log the names of the confirmed users
          confirmedTargetBookings.forEach(booking => {
            console.log(`Confirmed booking for user: ${booking.user_first_name} ${booking.user_last_name}`);
          });
        }

        // Calculate confirmed booking counts for each session
        const counts = {};
        sessionIds.forEach(sessionId => {
          const sessionBookings = allBookings.filter(b => b.session_id === sessionId);
          const confirmedBookings = sessionBookings.filter(b => b.status === 'confirmed');
          const count = confirmedBookings.reduce((sum, b) => sum + (b.participant_count || 1), 0);
          counts[sessionId] = count;

          console.log(`Session ${sessionId} has ${confirmedBookings.length} confirmed bookings with ${count} participants`);
        });

        // Update the state with the counts
        setSessionBookingCounts(counts);
        console.log('Updated session booking counts:', counts);

        // Special check for our target session
        if (counts[targetSessionId]) {
          console.log(`Target session ${targetSessionId} has ${counts[targetSessionId]} participants`);
        }
      } catch (err) {
        console.error('Error in fetching all sessions data:', err);
      }
    };

    fetchAllSessionsData();
  }, [sessions]); // Only depend on sessions changes

  const groupedSessions = useMemo(() => {
    const groups = {};
    const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');

    console.log('Grouping sessions for date:', selectedDateStr);
    console.log('All sessions before grouping:', sessions);

    // Handle case when sessions is undefined or empty
    if (sessions && sessions.length > 0) {
      sessions.forEach(session => {
        if (!session.start_time) {
          console.log('Session missing start_time:', session);
          return;
        }

        const date = format(parseISO(session.start_time), 'yyyy-MM-dd');
        console.log(`Session ${session.id} date: ${date}, selected date: ${selectedDateStr}, match: ${selectedDateStr === date}`);

        if (selectedDateStr === date) {
          if (!groups[date]) {
            groups[date] = [];
          }
          groups[date].push(session);
        }
      });
    }

    // If no sessions for selected date, add empty array
    if (!groups[selectedDateStr]) {
      groups[selectedDateStr] = [];
    }

    console.log('Grouped sessions:', groups);

    return groups;
  }, [sessions, selectedDate]);

  // Fetch participant profiles in batch for all confirmed bookings
  useEffect(() => {
    // Skip if there are no sessions
    if (!sessions || sessions.length === 0) {
      setParticipantProfiles({});
      return;
    }

    // Debug: Log all sessions and their bookings
    console.log('All sessions:', sessions);
    sessions.forEach(session => {
      console.log(`Session ${session.id} (${session.title}) bookings:`, session.bookings);
      const confirmedCount = (session.bookings || []).filter(b => b.status === 'confirmed').length;
      console.log(`Session ${session.id} has ${confirmedCount} confirmed bookings`);
    });

    const fetchProfiles = async () => {
      // Gather all unique user_ids from confirmed bookings in visible sessions
      const userIds = Array.from(new Set(
        sessions.flatMap(session =>
          (session.bookings || [])
            .filter(b => b.status === 'confirmed')
            .map(b => b.user_id)
        )
      ));

      console.log('Unique user IDs from confirmed bookings:', userIds);

      if (userIds.length === 0) {
        setParticipantProfiles({});
        return;
      }

      // Special check for our target session
      const targetSession = sessions.find(s => s.id === targetSessionId);
      if (targetSession) {
        const targetUserIds = (targetSession.bookings || [])
          .filter(b => b.status === 'confirmed')
          .map(b => b.user_id);
        console.log('Target session user IDs:', targetUserIds);
      }

      // Fetch all profiles in a single query
      const { data, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name')
        .in('id', userIds);
      if (error) {
        console.error('Error fetching profiles:', error);
        setParticipantProfiles({});
        return;
      }

      console.log('Fetched profiles:', data);

      // Map profiles by user_id
      const profileMap = {};
      for (const profile of data) {
        profileMap[profile.id] = profile;
      }

      // Special check for our target session profiles
      if (targetSession) {
        const targetUserIds = (targetSession.bookings || [])
          .filter(b => b.status === 'confirmed')
          .map(b => b.user_id);

        console.log('Target session profiles:');
        targetUserIds.forEach(userId => {
          console.log(`User ${userId}: ${profileMap[userId] ?
            `${profileMap[userId].first_name} ${profileMap[userId].last_name}` :
            'Profile not found'}`);
        });
      }

      setParticipantProfiles(profileMap);
    };
    fetchProfiles();
  }, [sessions, targetSessionId]); // Removed supabase from dependencies, added targetSessionId

  const handleCancelClick = (booking) => {
    setSelectedBooking(booking);
    setShowCancelModal(true);
  };

  const handleCancelConfirm = async () => {
    // Make a copy of the selectedBooking to use after the mutation completes
    // This prevents the "Cannot read properties of null" error
    const bookingCopy = { ...selectedBooking };

    try {
      setIsCancelling(true);

      if (!bookingCopy || !bookingCopy.id) {
        throw new Error('No booking selected or booking ID is missing');
      }

      // Use the React Query mutation instead of direct Supabase calls
      await cancelBookingMutation.mutateAsync(bookingCopy.id);

      // Success message will be shown based on the result
      // The mutation will handle all the database operations and cache invalidation

      // Directly update the sessionBookingCounts state to reflect the cancellation
      setSessionBookingCounts(prevCounts => {
        const sessionId = bookingCopy.session_id;
        if (prevCounts[sessionId] !== undefined) {
          // Decrement the count by 1 (or by participant_count if available)
          const decrementBy = bookingCopy.participant_count || 1;
          const newCount = Math.max(0, prevCounts[sessionId] - decrementBy);
          console.log(`Updating count for session ${sessionId}: ${prevCounts[sessionId]} -> ${newCount}`);
          return {
            ...prevCounts,
            [sessionId]: newCount
          };
        }
        return prevCounts;
      });

      // Close the modal
      setShowCancelModal(false);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error('Failed to cancel booking');
    } finally {
      setIsCancelling(false);
      setSelectedBooking(null);
    }
  };

  const handleBookSession = async (sessionId) => {
    try {
      setBookingInProgress(sessionId);

      await createBookingMutation.mutateAsync({
        sessionId,
        userId: user.id
      });

      // toast.success('Session booked successfully');

      // Invalidate all relevant queries with refetchType: 'all' to ensure immediate updates
      queryClient.invalidateQueries({
        queryKey: ['sessions'],
        refetchType: 'all',
        exact: false
      });
      queryClient.invalidateQueries({
        queryKey: ['membership', user?.id],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['loyalty_sessions', user?.id],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['loyalty_card_status', user?.id],
        refetchType: 'all'
      });
    } catch (error) {
      console.error('Error booking session:', error);
      toast.error(error.message || 'Failed to book session');
    } finally {
      setBookingInProgress(null);
    }
  };

  const isSessionBooked = (sessionId) => {
    const isBooked = bookings?.some(booking =>
      booking.session_id === sessionId &&
      booking.status !== 'cancelled'
    );

    console.log(`isSessionBooked check for session ${sessionId}: ${isBooked}`);
    return isBooked;
  };

  const getBookingForSession = (sessionId) => {
    const booking = bookings?.find(booking =>
      booking.session_id === sessionId &&
      booking.status !== 'cancelled'
    );

    console.log(`getBookingForSession for session ${sessionId}:`, booking);
    return booking;
  };

  const isLoading = isLoadingProfile || isLoadingBookings || isLoadingProp || isLoadingMembership; // Added isLoadingMembership

  if (isLoading) {
    return (
      <Container isLoading={isLoading}>
        <Spinner />
      </Container>
    );
  }

  const isDashboard = window.location.pathname === '/dashboard';
  const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');
  const sessionsForSelectedDate = groupedSessions[selectedDateStr] || [];

  // Check for our specific session of interest
  const targetSession = sessions.find(s => s.id === targetSessionId);

  if (targetSession) {
    console.log('TARGET SESSION FOUND IN PROPS:', targetSession);
    console.log('TARGET SESSION BOOKINGS IN PROPS:', targetSession.bookings);
    const confirmedBookings = targetSession.bookings?.filter(b => b.status === 'confirmed') || [];
    console.log('TARGET SESSION CONFIRMED BOOKINGS IN PROPS:', confirmedBookings);
    console.log('TARGET SESSION CONFIRMED COUNT IN PROPS:', confirmedBookings.length);
  } else {
    console.log('TARGET SESSION NOT FOUND IN CURRENT VIEW');
  }

  if (!sessionsForSelectedDate.length) {
    return (
      <NoSessions>
        <h3>No Sessions Available</h3>
        <p>No sessions available for {format(selectedDate, 'EEEE, MMMM d, yyyy')}</p>
        {isDashboard && <Link to="/booking">Book your next session</Link>}
      </NoSessions>
    );
  }

  return (
    <Container isLoading={isLoading}>
      {showCancelModal && (
        <CancelModal
          onConfirm={handleCancelConfirm}
          onCancel={() => setShowCancelModal(false)}
          isLoading={isCancelling}
        />
      )}
      {Object.entries(groupedSessions).map(([date, dateSessions]) => {
        // Ensure we have valid sessions with start and end times
        if (!dateSessions.length || !dateSessions[0].start_time || !dateSessions[dateSessions.length - 1].end_time) {
          return null;
        }

        return (
          <DateGroup key={date}>
            <DateHeader>
              {format(parseISO(dateSessions[0].start_time), 'h:mm a')} - {format(parseISO(dateSessions[dateSessions.length - 1].end_time), 'h:mm a')}
              <span>{dateSessions.length} session{dateSessions.length !== 1 ? 's' : ''}</span>
            </DateHeader>
            <SessionsGrid>
              {dateSessions.map(session => {
                const isBooked = isSessionBooked(session.id);
                const booking = getBookingForSession(session.id);
                const isPast = isBefore(parseISO(session.start_time), new Date());

                // Calculate confirmed participants
                console.log(`Session ${session.id} raw bookings:`, session.bookings);

                // Check if bookings is undefined or not an array
                if (!session.bookings || !Array.isArray(session.bookings)) {
                  console.error(`Session ${session.id} has invalid bookings:`, session.bookings);
                  session.bookings = []; // Fix it to avoid errors
                }

                // Get confirmed bookings, ensuring we have the latest data
                const confirmedBookings = session.bookings?.filter(b => b.status === 'confirmed') || [];
                console.log(`Session ${session.id} confirmed bookings after filter:`, confirmedBookings);

                // Special check for target session to ensure we have the latest data
                if (session.id === targetSessionId) {
                  console.log(`Target session ${targetSessionId} has ${confirmedBookings.length} confirmed bookings`);
                }

                // Start with 0 for the count
                let confirmedParticipantCount = confirmedBookings.reduce((sum, b) => {
                  console.log(`Adding participant count for booking ${b.id}:`, b.participant_count || 1);
                  return sum + (b.participant_count || 1);
                }, 0);

                // This initial check will be updated after we potentially correct the count
                let isFull = confirmedParticipantCount >= session.max_participants;

                // Debug info
                console.log(`Session ${session.id} (${session.title}): ${confirmedParticipantCount}/${session.max_participants} participants`);
                console.log('Confirmed bookings:', confirmedBookings);

                // Use our accurate booking counts if available
                const accurateCount = sessionBookingCounts[session.id];
                if (typeof accurateCount === 'number') {
                  if (confirmedParticipantCount !== accurateCount) {
                    console.log(`FIXING COUNT for session ${session.id}: ${confirmedParticipantCount} -> ${accurateCount}`);
                    // Update the count with the accurate value from our direct database query
                    confirmedParticipantCount = accurateCount;
                  }
                }

                // Special case for our target session - don't force it to 2 anymore
                // Instead, use the actual count from the database
                if (session.id === '0757c459-3bc5-4249-8b2a-c3b3a258aa59') {
                  console.log(`Target session count: ${confirmedParticipantCount}`);
                }

                // Update the isFull check based on the corrected participant count
                isFull = confirmedParticipantCount >= session.max_participants;

                // Special check for our target session
                if (session.id === '0757c459-3bc5-4249-8b2a-c3b3a258aa59') {
                  console.log('RENDERING TARGET SESSION WITH PARTICIPANT COUNT:', confirmedParticipantCount);
                  console.log('TARGET SESSION BOOKINGS IN RENDER:', session.bookings);
                  console.log('TARGET SESSION CONFIRMED BOOKINGS IN RENDER:', confirmedBookings);
                  console.log('TARGET SESSION IS FULL:', isFull);
                }

                return (
                  <SessionCard
                    key={session.id}
                    $isBooked={isBooked}
                    $isPast={isPast}
                    $isFull={isFull}
                  >
                    {isDashboard && (
                      <Link to="/booking" style={{ marginTop: 0 }}>
                        Go to Bookings
                      </Link>
                    )}
                    <SessionTime>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 6v6l4 2"/>
                      </svg>
                      <span>
                        {format(parseISO(session.start_time), 'EEEE, MMMM d')} • {format(parseISO(session.start_time), 'h:mm a')} - {format(parseISO(session.end_time), 'h:mm a')}
                      </span>
                    </SessionTime>
                    <SessionTitle>
                      {session.title || session.type}
                      <span style={{width: 10,display:'flex'}}/>
                      {isFull && <Badge $type="full">FULL</Badge>}
                      {isBooked && <Badge $type="booked">BOOKED</Badge>}
                      {!isBooked && !isFull && <Badge $type="available">AVAILABLE</Badge>}
                    </SessionTitle>
                    <SessionDetails>
                      <div className="info-row">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                          <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <span className="label">Coach:</span>
                        <span className="value">{session.coach ? `${session.coach.first_name || ''} ${session.coach.last_name || ''}`.trim() : 'TBA'}</span>
                      </div>
                      <div className="info-row">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                          <circle cx="12" cy="10" r="3"/>
                        </svg>
                        <span className="label">Location:</span>
                        <span className="value">{session.school}</span>
                      </div>
                      <div className="info-row">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                          <circle cx="9" cy="7" r="4"/>
                          <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        <span className="label">Spots:</span>
                        <span className="value">{confirmedParticipantCount}/{session.max_participants}</span>
                      </div>
                      <ParticipantsList>
                        <ParticipantsHeader>
                          Participants ({confirmedParticipantCount})
                        </ParticipantsHeader>
                        <ParticipantsNames>
                          {/* Only show participants if there are any */}
                          {confirmedParticipantCount > 0 && confirmedBookings.length > 0 ? (
                            confirmedBookings.map(participant => {
                              // Debug log for each participant
                              console.log(`Rendering participant: ${participant.user_id}`, participant);
                              console.log(`Profile for ${participant.user_id}:`, participantProfiles[participant.user_id]);

                              return (
                                <ParticipantRow key={participant.user_id}>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="1.5"
                                  >
                                    <rect x="8" y="4" width="8" height="11" rx="4" />
                                    <rect x="10" y="15" width="4" height="6" rx="1" />
                                    <circle cx="12" cy="9" r="2" />
                                  </svg>
                                  <span style={{
                                    fontWeight: participant.user_id === user.id ? theme.fontWeights.bold : theme.fontWeights.normal
                                  }}>
                                    {/* Try to get the name from the participant object first (from bookings_with_users) */}
                                    {participant.user_first_name && participant.user_last_name
                                      ? `${participant.user_first_name} ${participant.user_last_name}`
                                      : participantProfiles[participant.user_id]
                                        ? `${participantProfiles[participant.user_id].first_name || ''} ${participantProfiles[participant.user_id].last_name || ''}`.trim()
                                        : `User ${participant.user_id.substring(0, 8)}...`}
                                  </span>
                                </ParticipantRow>
                              );
                            })
                          ) : (
                            <ParticipantRow>
                              <span>No participants yet</span>
                            </ParticipantRow>
                          )}
                        </ParticipantsNames>
                      </ParticipantsList>
                    </SessionDetails>
                    <ButtonGroup>
                      {isBooked ? (
                        (() => {
                          const cancellationCheck = canCancelBooking(booking);
                          return (
                            <Button
                              className="cancel"
                              $secondary
                              onClick={() => handleCancelClick(booking)}
                              disabled={!cancellationCheck.canCancel || cancelBookingMutation.isPending}
                              title={!cancellationCheck.canCancel ? cancellationCheck.reason : ''}
                            >
                              {cancelBookingMutation.isPending
                                ? 'Cancelling...'
                                : !cancellationCheck.canCancel
                                  ? 'Cannot Cancel'
                                  : 'Cancel Booking'
                              }
                            </Button>
                          );
                        })()
                      ) : (
                        <Button
                          className="book-now"
                          onClick={() => handleBookSession(session.id)}
                          disabled={isPast || isFull || bookingInProgress === session.id || !!membershipDetails?.isExpired}
                        >
                          {bookingInProgress === session.id ? 'Booking...' : (isFull ? 'Class Full' : 'Book Now')}
                        </Button>
                      )}
                    </ButtonGroup>
                  </SessionCard>
                );
              })}
            </SessionsGrid>
          </DateGroup>
        );
      })}
    </Container>
  );
};

SessionsAndBookings.propTypes = {
  selectedDate: PropTypes.instanceOf(Date).isRequired,
  sessions: PropTypes.array,
  isLoadingProp: PropTypes.bool
};

export default SessionsAndBookings;
