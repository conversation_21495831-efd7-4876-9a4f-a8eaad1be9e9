import { useState, useMemo, useEffect } from 'react';
import styled from '@emotion/styled';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import { toast } from 'react-hot-toast';
import { useUsers, useToggleAdmin, useToggleCoach, useDeleteUser } from '../hooks/useAdminQuery';
import Spinner from './Spinner';
import PropTypes from 'prop-types';
import { supabase } from '../lib/supabase';
import React from 'react';

const Container = styled.div`
  padding: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }

  h2 {
    font-size: ${theme.fontSizes.xl};
    color: ${theme.colors.secondary};
    margin-bottom: ${theme.spacing.lg};

    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes['2xl']};
    }
  }
`;

const SearchContainer = styled.div`
  margin-bottom: ${theme.spacing.lg};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  background: ${theme.colors.background};
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 2px ${theme.colors.primary}20;
  }

  &::placeholder {
    color: ${theme.colors.textLight};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    max-width: 300px;
  }
`;

const TableContainer = styled.div`
  margin-top: ${theme.spacing.lg};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
`;

const DesktopTable = styled.div`
  display: none;

  @media (min-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const Table = styled.div`
  width: 100%;
  min-width: 600px;

  .table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 2fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    background: ${theme.colors.background};
    border-bottom: 1px solid ${theme.colors.border};

    span {
      font-weight: ${theme.fontWeights.semibold};
      color: ${theme.colors.secondary};
      font-size: ${theme.fontSizes.sm};
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 2fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    border-bottom: 1px solid ${theme.colors.border};
    align-items: center;

    &:hover {
      background: ${theme.colors.background};
    }

    > div {
      font-size: ${theme.fontSizes.sm};
      color: ${theme.colors.text};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};

      &:last-child {
        justify-content: flex-start;
        gap: ${theme.spacing.md};
      }
    }
  }
`;

const MobileTable = styled.div`
  display: block;

  @media (min-width: ${theme.breakpoints.md}) {
    display: none;
  }
`;

const UserCard = styled.div`
  background: ${props => props.isCurrentUser ? `${theme.colors.primary}10` : 'white'};
  border: 1px solid ${props => props.isCurrentUser ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.md};
  transition: all 0.2s ease;
  box-shadow: ${theme.shadows.sm};

  &:hover {
    box-shadow: ${theme.shadows.md};
    transform: translateY(-1px);
  }

  .user-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: ${theme.spacing.md};
    flex-wrap: wrap;

    p {
      margin: ${theme.spacing.xs} 0;
      color: ${theme.colors.text};

      strong {
        color: ${props => props.isCurrentUser ? theme.colors.primary : theme.colors.secondary};
        font-weight: ${theme.fontWeights.semibold};
      }
    }
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  margin-top: ${theme.spacing.lg};
  flex-wrap: wrap;

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    width: 100%;
  }
`;

const ActionButton = styled.button`
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: none;
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.xs};
  box-shadow: ${theme.shadows.sm};

  @media (max-width: ${theme.breakpoints.sm}) {
    width: 100%;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
  }

  &.admin {
    background: ${props => props.isAdmin ? theme.colors.error : theme.colors.success};
    color: white;

    &:hover:not(:disabled) {
      opacity: 0.9;
      box-shadow: ${theme.shadows.md};
      transform: translateY(-1px);
    }
  }

  &.coach {
    background: ${props => props.isCoach ? theme.colors.error : theme.colors.info};
    color: white;

    &:hover:not(:disabled) {
      opacity: 0.9;
      box-shadow: ${theme.shadows.md};
      transform: translateY(-1px);
    }
  }

  &.delete {
    background: ${theme.colors.error};
    color: white;

    &:hover:not(:disabled) {
      opacity: 0.9;
      box-shadow: ${theme.shadows.md};
      transform: translateY(-1px);
    }
  }
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid ${theme.colors.background};
  border-top: 2px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xl};
  color: ${theme.colors.textLight};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};

  p {
    font-size: ${theme.fontSizes.lg};
    margin: 0;
  }
`;

// Simple modal for editing user plan
const ModalOverlay = styled.div`
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;
const ModalContent = styled.div`
  background: white;
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  min-width: 320px;
  max-width: 95vw;
  box-shadow: ${theme.shadows.lg};
`;
function EditPlanModal({ user, open, onClose, onSave, override }) {
  const [form, setForm] = useState(() => ({
    membership_type: user?.membership_type || '',
    membership_status: user?.membership_status || '',
    membership_start_date: user?.membership_start_date ? user.membership_start_date.slice(0,10) : '',
    membership_end_date: user?.membership_end_date ? user.membership_end_date.slice(0,10) : '',
    loyalty_sessions: (override && override.loyalty_sessions != null) ? override.loyalty_sessions : (user?.loyalty_sessions || 0),
    once_off_sessions: user?.once_off_sessions || 0,
  }));
  const [saving, setSaving] = useState(false);

  React.useEffect(() => {
    if (user) {
      setForm(f => ({
        ...f,
        membership_type: user.membership_type || '',
        membership_status: user.membership_status || '',
        membership_start_date: user.membership_start_date ? user.membership_start_date.slice(0,10) : '',
        membership_end_date: user.membership_end_date ? user.membership_end_date.slice(0,10) : '',
        loyalty_sessions: (override && override.loyalty_sessions != null) ? override.loyalty_sessions : (user.loyalty_sessions || 0),
        once_off_sessions: user.once_off_sessions || 0,
      }));
    }
  }, [user, override]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    try {
      const updates = {
        membership_type: form.membership_type,
        membership_status: form.membership_status,
        membership_start_date: form.membership_start_date || null,
        membership_end_date: form.membership_end_date || null,
      };
      if (type.includes('loyalty')) {
        updates.loyalty_sessions = form.loyalty_sessions;
      }
      if (type.includes('once-off')) {
        updates.once_off_sessions = form.once_off_sessions;
      }
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);
      if (error) throw error;
      toast.success('Plan updated successfully!');
      setTimeout(() => {
        setSaving(false);
        onSave();
      }, 100);
    } catch (err) {
      toast.error('Failed to update plan: ' + (err.message || err));
      setSaving(false);
    }
  };

  if (!open) return null;
  const type = (form.membership_type || '').toLowerCase();
  const showTypeAndSessions = type.includes('loyalty') || type.includes('once-off');

  return (
    <ModalOverlay>
      <ModalContent>
        <h3>Edit Plan for {user.first_name} {user.last_name}</h3>
        <form onSubmit={handleSubmit}>
          {showTypeAndSessions && (
            <>
              <label>Membership Type:<br/>
                <select value={form.membership_type} onChange={e => setForm(f => ({...f, membership_type: e.target.value}))}>
                  <option value="">None</option>
                  <option value="loyalty">Loyalty</option>
                  <option value="once-off">Once-off</option>
                </select>
              </label><br/>
              {type.includes('loyalty') && (
                <label>Loyalty Sessions:<br/>
                  <input type="number" min="0" max="6" value={form.loyalty_sessions} onChange={e => {
                    let val = +e.target.value;
                    if (val < 0) val = 0;
                    if (val > 6) val = 6;
                    setForm(f => ({...f, loyalty_sessions: val}));
                  }} />
                </label>
              )}
              {type.includes('once-off') && (
                <label>Once-off Sessions:<br/>
                  <input type="number" min="0" value={form.once_off_sessions} onChange={e => {
                    let val = +e.target.value;
                    if (val < 0) val = 0;
                    setForm(f => ({...f, once_off_sessions: val}));
                  }} />
                </label>
              )}
            </>
          )}
          {!showTypeAndSessions && (
            <>
              <label>Status:<br/>
                <select value={form.membership_status} onChange={e => setForm(f => ({...f, membership_status: e.target.value}))}>
                  <option value="inactive">Inactive</option>
                  <option value="active">Active</option>
                </select>
              </label><br/>
              <label>Start Date:<br/>
                <input type="date" value={form.membership_start_date} onChange={e => setForm(f => ({...f, membership_start_date: e.target.value}))} />
              </label><br/>
              <label>End Date:<br/>
                <input type="date" value={form.membership_end_date} onChange={e => setForm(f => ({...f, membership_end_date: e.target.value}))} />
              </label><br/>
            </>
          )}
          <div style={{marginTop: 16, display: 'flex', gap: 8}}>
            <button type="submit" disabled={saving}>Save</button>
            <button type="button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </ModalContent>
    </ModalOverlay>
  );
}

EditPlanModal.propTypes = {
  user: PropTypes.object,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  override: PropTypes.object,
};

const UserRoleManagement = () => {
  const { user: currentUser } = useAuth();
  const { data: users = [], isLoading, isError, error } = useUsers();
  const toggleAdminMutation = useToggleAdmin();
  const toggleCoachMutation = useToggleCoach();
  const deleteUserMutation = useDeleteUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [editUser, setEditUser] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [loyaltySessions, setLoyaltySessions] = useState({});
  const [editFormOverride, setEditFormOverride] = useState(null);

  const handleToggleAdmin = async (userId) => {
    try {
      await toggleAdminMutation.mutateAsync(userId);
      toast.success('User admin status updated successfully!');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error.message || 'Failed to update user admin status');
    }
  };

  const handleToggleCoach = async (userId) => {
    try {
      await toggleCoachMutation.mutateAsync(userId);
      toast.success('User coach status updated successfully!');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error.message || 'Failed to update user coach status');
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      await deleteUserMutation.mutateAsync(userId);
      toast.success('User deleted successfully');
    } catch (error) {
      console.error('Error deleting user:', error);
      // Only show error toast if the error is not a 404 (user already deleted)
      if (!error.message?.includes('404')) {
        toast.error(error.message || 'Failed to delete user');
      }
    }
  };

  // Filter and sort users
  const filteredUsers = useMemo(() => {
    return users
      .filter(user => {
        const searchLower = searchTerm.toLowerCase();
        return (
          user.email.toLowerCase().includes(searchLower) ||
          (user.first_name && user.first_name.toLowerCase().includes(searchLower)) ||
          (user.last_name && user.last_name.toLowerCase().includes(searchLower))
        );
      })
      .sort((a, b) => {
        // Always keep current user at top
        if (a.id === currentUser?.id) return -1;
        if (b.id === currentUser?.id) return 1;

        // Then sort by email
        return a.email.localeCompare(b.email);
      });
  }, [users, searchTerm, currentUser]);

  useEffect(() => {
    async function fetchLoyaltySessions() {
      const results = {};
      for (const user of users) {
        const type = (user.membership_type || '').toLowerCase();
        if (type.includes('loyalty')) {
          // Get latest completed loyalty-card payment
          const { data: payment, error: paymentError } = await supabase
            .from('payments')
            .select('*')
            .eq('user_id', user.id)
            .eq('plan_id', 'loyalty-card')
            .eq('status', 'completed')
            .order('payment_date', { ascending: false })
            .limit(1)
            .single();
          if (payment && !paymentError) {
            console.log('Loyalty payment ID for user', user.email, ':', payment.id);
            const { data: used, error: usedError } = await supabase.rpc('get_used_loyalty_sessions', { payment_id_input: payment.id });
            console.log('Loyalty RPC result:', used);
            let usedCount = 0;
            if (!usedError) {
              if (typeof used === 'number') {
                usedCount = used;
              } else if (Array.isArray(used) && typeof used[0] === 'number') {
                usedCount = used[0];
              }
              results[user.id] = 6 - usedCount;
            } else {
              results[user.id] = 'Err';
            }
          } else {
            results[user.id] = 'N/A';
          }
        } else {
          results[user.id] = 'N/A';
        }
      }
      setLoyaltySessions(results);
    }
    if (users && users.length) fetchLoyaltySessions();
  }, [users]);

  if (isLoading) {
    return (
      <Container>
        <EmptyState>
          <Spinner />
        </EmptyState>
      </Container>
    );
  }

  if (isError) {
    return (
      <Container>
        <EmptyState>
          <p>Error loading users: {error?.message || 'Something went wrong'}</p>
        </EmptyState>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <EmptyState>
          <h2>Error loading users</h2>
          <p>{error}</p>
        </EmptyState>
      </Container>
    );
  }

  return (
    <Container>
      <h2>User Role Management</h2>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {!filteredUsers.length ? (
        <EmptyState>
          <p>No users found matching your search.</p>
        </EmptyState>
      ) : (
        <>
          <MobileTable>
            {filteredUsers.map(user => (
              <UserCard key={user.id} isCurrentUser={user.id === currentUser?.id}>
                <div className="user-info">
                  <h3>{user.first_name || 'No Name'} {user.last_name}</h3>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Plan:</strong> {user.membership_type || 'None'} ({user.membership_status || 'inactive'})</p>
                  <p><strong>Start:</strong> {user.membership_start_date ? new Date(user.membership_start_date).toLocaleDateString() : '-'}</p>
                  {user.membership_end_date && (
                    <p><strong>End:</strong> {new Date(user.membership_end_date).toLocaleDateString()}</p>
                  )}
                  <p><strong>Loyalty Sessions Remaining:</strong> {loyaltySessions[user.id] ?? '...'}</p>
                  <p><strong>Admin:</strong> {user.is_admin ? 'Yes' : 'No'}  <strong>Coach:</strong> {user.is_coach ? 'Yes' : 'No'}</p>
                </div>
                {currentUser?.email !== user.email && (
                  <ButtonContainer>
                    <ActionButton
                      className="admin"
                      onClick={() => handleToggleAdmin(user.id)}
                      disabled={toggleAdminMutation.isPending || user.id === currentUser?.id}
                      isAdmin={user.is_admin}
                    >
                      {toggleAdminMutation.isPending && toggleAdminMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : user.is_admin ? (
                        'Remove Admin'
                      ) : (
                        'Make Admin'
                      )}
                    </ActionButton>
                    <ActionButton
                      className="coach"
                      onClick={() => handleToggleCoach(user.id)}
                      disabled={toggleCoachMutation.isPending || user.id === currentUser?.id}
                      isCoach={user.is_coach}
                    >
                      {toggleCoachMutation.isPending && toggleCoachMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : user.is_coach ? (
                        'Remove Coach'
                      ) : (
                        'Make Coach'
                      )}
                    </ActionButton>
                    <ActionButton
                      className="delete"
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={deleteUserMutation.isPending || user.id === currentUser?.id}
                    >
                      {deleteUserMutation.isPending && deleteUserMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : (
                        'Delete'
                      )}
                    </ActionButton>
                    {/* <ActionButton
                      className="edit"
                      onClick={() => {
                        const type = (user.membership_type || '').toLowerCase();
                        if (type.includes('loyalty')) {
                          setEditFormOverride({ loyalty_sessions: loyaltySessions[user.id] });
                        } else {
                          setEditFormOverride(null);
                        }
                        setEditUser(user);
                        setEditModalOpen(true);
                      }}
                    >
                      Edit Plan
                    </ActionButton> */}
                  </ButtonContainer>
                )}
              </UserCard>
            ))}
          </MobileTable>

          <DesktopTable>
            <TableContainer>
              <Table>
                <div className="table-header">
                  <span>Email</span>
                  <span>Name</span>
                  <span>Plan</span>
                  <span>Start</span>
                  <span>End</span>
                  <span>Loyalty Sessions</span>
                  <span>Admin</span>
                  <span>Coach</span>
                  <span>Actions</span>
                </div>
                {filteredUsers.map(user => (
                  <div
                    key={user.id}
                    className="table-row"
                    style={{
                      background: user.id === currentUser?.id ? `${theme.colors.primary}10` : 'inherit',
                    }}
                  >
                    <div>{user.email}</div>
                    <div>{user.first_name} {user.last_name}</div>
                    <div>{user.membership_type || 'None'} ({user.membership_status || 'inactive'})</div>
                    <div>{user.membership_start_date ? new Date(user.membership_start_date).toLocaleDateString() : '-'}</div>
                    <div>{user.membership_end_date ? new Date(user.membership_end_date).toLocaleDateString() : ''}</div>
                    <div>{loyaltySessions[user.id] ?? '...'}</div>
                    <div>{user.is_admin ? 'Yes' : 'No'}</div>
                    <div>{user.is_coach ? 'Yes' : 'No'}</div>
                    <div>
                      {currentUser?.email !== user.email && (
                        <ButtonContainer>
                          <ActionButton
                            className="admin"
                            onClick={() => handleToggleAdmin(user.id)}
                            disabled={toggleAdminMutation.isPending || user.id === currentUser?.id}
                            isAdmin={user.is_admin}
                          >
                            {toggleAdminMutation.isPending && toggleAdminMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : user.is_admin ? (
                              'Remove Admin'
                            ) : (
                              'Make Admin'
                            )}
                          </ActionButton>
                          {user.is_admin && <ActionButton
                            className="coach"
                            onClick={() => handleToggleCoach(user.id)}
                            disabled={!user.is_admin || toggleCoachMutation.isPending || user.id === currentUser?.id}
                            isCoach={user.is_coach}
                          >
                            {toggleCoachMutation.isPending && toggleCoachMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : user.is_coach ? (
                              'Remove Coach'
                            ) : (
                              'Make Coach'
                            )}
                          </ActionButton>}
                          <ActionButton
                            className="delete"
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={deleteUserMutation.isPending || user.id === currentUser?.id}
                          >
                            {deleteUserMutation.isPending && deleteUserMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : (
                              'Delete'
                            )}
                          </ActionButton>
                          {/* <ActionButton
                            className="edit"
                            onClick={() => {
                              const type = (user.membership_type || '').toLowerCase();
                              if (type.includes('loyalty')) {
                                setEditFormOverride({ loyalty_sessions: loyaltySessions[user.id] });
                              } else {
                                setEditFormOverride(null);
                              }
                              setEditUser(user);
                              setEditModalOpen(true);
                            }}
                          >
                            Edit Plan
                          </ActionButton> */}
                        </ButtonContainer>
                      )}
                    </div>
                  </div>
                ))}
              </Table>
            </TableContainer>
          </DesktopTable>
          <EditPlanModal
            user={editUser}
            open={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            override={editFormOverride}
            onSave={() => {
              toast.success('Plan updated successfully!');
              setEditModalOpen(false);
              setEditFormOverride(null);
            }}
          />
        </>
      )}
    </Container>
  );
};

export default UserRoleManagement;
