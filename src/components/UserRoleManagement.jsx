import { useState, useMemo, useEffect } from 'react';
import styled from '@emotion/styled';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import { toast } from 'react-hot-toast';
import { useUsers, useToggleAdmin, useToggleCoach, useDeleteUser } from '../hooks/useAdminQuery';
import Spinner from './Spinner';
import PropTypes from 'prop-types';
import { supabase } from '../lib/supabase';
import React from 'react';

const Container = styled.div`
  padding: ${theme.spacing.md};
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100vw;
  box-sizing: border-box;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const HeaderSection = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  margin-bottom: ${theme.spacing.xl};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  h2 {
    font-size: ${theme.fontSizes.xl};
    color: ${theme.colors.primary};
    margin: 0 0 ${theme.spacing.md};
    font-weight: ${theme.fontWeights.bold};
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};

    &::before {
      content: '👥';
      font-size: ${theme.fontSizes.lg};
    }

    @media (min-width: ${theme.breakpoints.md}) {
      font-size: ${theme.fontSizes['2xl']};
    }
  }

  p {
    color: ${theme.colors.textLight};
    margin: 0;
    font-size: ${theme.fontSizes.md};
    font-weight: ${theme.fontWeights.medium};

    &::before {
      content: '⚙️';
      margin-right: ${theme.spacing.xs};
    }
  }
`;

const SearchContainer = styled.div`
  margin-bottom: ${theme.spacing.xl};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: ${theme.borderRadius.lg};
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  font-weight: ${theme.fontWeights.medium};

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &::placeholder {
    color: ${theme.colors.textLight};
    font-weight: ${theme.fontWeights.normal};

    &::before {
      content: '🔍';
      margin-right: ${theme.spacing.xs};
    }
  }

  @media (min-width: ${theme.breakpoints.md}) {
    max-width: 400px;
  }
`;

const TableContainer = styled.div`
  margin-top: ${theme.spacing.lg};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
`;

const DesktopTable = styled.div`
  display: none;

  @media (min-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const Table = styled.div`
  width: 100%;
  min-width: 1000px;
  overflow-x: auto;

  .table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1.5fr 1fr 1fr 1.5fr 1fr 1fr 2fr;
    padding: ${theme.spacing.md} ${theme.spacing.lg};
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: ${theme.borderRadius.lg} ${theme.borderRadius.lg} 0 0;

    @media (min-width: ${theme.breakpoints.lg}) {
      padding: ${theme.spacing.lg} ${theme.spacing.xl};
    }

    span {
      font-weight: ${theme.fontWeights.bold};
      color: ${theme.colors.primary};
      font-size: ${theme.fontSizes.xs};
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: ${theme.spacing.xs};
      word-break: break-word;
      overflow-wrap: break-word;

      @media (min-width: ${theme.breakpoints.lg}) {
        font-size: ${theme.fontSizes.sm};
      }

      &::before {
        font-size: ${theme.fontSizes.xs};
        flex-shrink: 0;
      }

      &:nth-of-type(1)::before { content: '📧'; }
      &:nth-of-type(2)::before { content: '👤'; }
      &:nth-of-type(3)::before { content: '💳'; }
      &:nth-of-type(4)::before { content: '📅'; }
      &:nth-of-type(5)::before { content: '⏰'; }
      &:nth-of-type(6)::before { content: '🎯'; }
      &:nth-of-type(7)::before { content: '👑'; }
      &:nth-of-type(8)::before { content: '🏆'; }
      &:nth-of-type(9)::before { content: '⚡'; }
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1.5fr 1fr 1fr 1.5fr 1fr 1fr 2fr;
    padding: ${theme.spacing.sm} ${theme.spacing.lg};
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    align-items: center;
    transition: all 0.2s ease;

    @media (min-width: ${theme.breakpoints.lg}) {
      padding: ${theme.spacing.md} ${theme.spacing.xl};
    }

    &:hover {
      background: rgba(248, 250, 252, 0.8);
      transform: translateX(4px);
    }

    &:last-child {
      border-bottom: none;
      border-radius: 0 0 ${theme.borderRadius.lg} ${theme.borderRadius.lg};
    }

    > div {
      font-size: ${theme.fontSizes.xs};
      color: ${theme.colors.text};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.xs};
      font-weight: ${theme.fontWeights.medium};
      word-break: break-word;
      overflow-wrap: break-word;
      min-width: 0;

      @media (min-width: ${theme.breakpoints.lg}) {
        font-size: ${theme.fontSizes.sm};
        gap: ${theme.spacing.sm};
      }

      &:last-child {
        justify-content: flex-start;
        gap: ${theme.spacing.xs};
        flex-wrap: wrap;
      }
    }
  }
`;

const MobileTable = styled.div`
  display: block;

  @media (min-width: ${theme.breakpoints.md}) {
    display: none;
  }
`;

const UserCard = styled.div`
  background: ${props => props.isCurrentUser
    ? `linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.primary}25)`
    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8))'};
  border: 1px solid ${props => props.isCurrentUser ? theme.colors.primary : 'rgba(255, 255, 255, 0.8)'};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.lg};
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.lg};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.isCurrentUser
      ? `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary})`
      : `linear-gradient(90deg, ${theme.colors.success}, #059669)`};
  }

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);
  }

  .user-info {
    display: grid;
    gap: ${theme.spacing.sm};

    @media (min-width: ${theme.breakpoints.sm}) {
      gap: ${theme.spacing.md};
    }

    h3 {
      color: ${theme.colors.primary};
      font-size: ${theme.fontSizes.md};
      font-weight: ${theme.fontWeights.bold};
      margin: 0 0 ${theme.spacing.md};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};
      word-break: break-word;
      overflow-wrap: break-word;

      @media (min-width: ${theme.breakpoints.sm}) {
        font-size: ${theme.fontSizes.lg};
      }

      &::before {
        content: ${props => props.isCurrentUser ? "'👑'" : "'👤'"};
        font-size: ${theme.fontSizes.sm};
        flex-shrink: 0;

        @media (min-width: ${theme.breakpoints.sm}) {
          font-size: ${theme.fontSizes.md};
        }
      }
    }

    p {
      margin: ${theme.spacing.xs} 0;
      color: ${theme.colors.text};
      font-size: ${theme.fontSizes.xs};
      font-weight: ${theme.fontWeights.medium};
      display: flex;
      flex-direction: column;
      gap: ${theme.spacing.xs};
      padding: ${theme.spacing.sm};
      background: rgba(248, 250, 252, 0.5);
      border-radius: ${theme.borderRadius.sm};
      transition: all 0.2s ease;
      word-break: break-word;
      overflow-wrap: break-word;

      @media (min-width: ${theme.breakpoints.sm}) {
        font-size: ${theme.fontSizes.sm};
        flex-direction: row;
        align-items: center;
        gap: ${theme.spacing.sm};
      }

      &:hover {
        background: rgba(248, 250, 252, 0.8);
        transform: translateX(4px);
      }

      strong {
        color: ${props => props.isCurrentUser ? theme.colors.primary : theme.colors.secondary};
        font-weight: ${theme.fontWeights.bold};
        min-width: 80px;
        display: flex;
        align-items: center;
        gap: ${theme.spacing.xs};
        flex-shrink: 0;

        @media (min-width: ${theme.breakpoints.sm}) {
          min-width: 120px;
        }

        &::before {
          font-size: ${theme.fontSizes.xs};
          flex-shrink: 0;
        }
      }

      .value {
        word-break: break-all;
        overflow-wrap: break-word;
        min-width: 0;
        flex: 1;
      }

      &:nth-of-type(1) strong::before { content: '📧'; }
      &:nth-of-type(2) strong::before { content: '💳'; }
      &:nth-of-type(3) strong::before { content: '📅'; }
      &:nth-of-type(4) strong::before { content: '⏰'; }
      &:nth-of-type(5) strong::before { content: '🎯'; }
      &:nth-of-type(6) strong::before { content: '⚙️'; }
    }
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
  margin-top: ${theme.spacing.xl};
  padding-top: ${theme.spacing.lg};
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    width: 100%;
    gap: ${theme.spacing.xs};
  }
`;

const ActionButton = styled.button`
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border: none;
  border-radius: ${theme.borderRadius.lg};
  font-size: ${theme.fontSizes.xs};
  font-weight: ${theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.xs};
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  flex: 1;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    font-size: ${theme.fontSizes.sm};
    min-width: 120px;
    flex: none;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    width: 100%;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }

  &.admin {
    background: ${props => props.isAdmin
      ? `linear-gradient(135deg, ${theme.colors.error}, #dc2626)`
      : `linear-gradient(135deg, ${theme.colors.success}, #059669)`};
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &::after {
      content: ${props => props.isAdmin ? "'👑'" : "'⬆️'"};
      margin-left: ${theme.spacing.xs};
    }
  }

  &.coach {
    background: ${props => props.isCoach
      ? `linear-gradient(135deg, ${theme.colors.error}, #dc2626)`
      : `linear-gradient(135deg, ${theme.colors.info}, #0ea5e9)`};
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &::after {
      content: ${props => props.isCoach ? "'🏆'" : "'⬆️'"};
      margin-left: ${theme.spacing.xs};
    }
  }

  &.delete {
    background: linear-gradient(135deg, ${theme.colors.error}, #dc2626);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
    }

    &::after {
      margin-left: ${theme.spacing.xs};
    }
  }
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid ${theme.colors.background};
  border-top: 2px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing['4xl']};
  color: ${theme.colors.textLight};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin: 0 auto;

  &::before {
    content: '👥';
    display: block;
    font-size: ${theme.fontSizes.xxl};
    margin-bottom: ${theme.spacing.lg};
  }

  h3 {
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.xl};
    margin: 0 0 ${theme.spacing.md};
    font-weight: ${theme.fontWeights.bold};
  }

  p {
    font-size: ${theme.fontSizes.md};
    margin: 0;
    font-weight: ${theme.fontWeights.medium};
  }
`;

// Simple modal for editing user plan
const ModalOverlay = styled.div`
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;
const ModalContent = styled.div`
  background: white;
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  min-width: 320px;
  max-width: 95vw;
  box-shadow: ${theme.shadows.lg};
`;
function EditPlanModal({ user, open, onClose, onSave, override }) {
  const [form, setForm] = useState(() => ({
    membership_type: user?.membership_type || '',
    membership_status: user?.membership_status || '',
    membership_start_date: user?.membership_start_date ? user.membership_start_date.slice(0,10) : '',
    membership_end_date: user?.membership_end_date ? user.membership_end_date.slice(0,10) : '',
    loyalty_sessions: (override && override.loyalty_sessions != null) ? override.loyalty_sessions : (user?.loyalty_sessions || 0),
    once_off_sessions: user?.once_off_sessions || 0,
  }));
  const [saving, setSaving] = useState(false);

  React.useEffect(() => {
    if (user) {
      setForm(f => ({
        ...f,
        membership_type: user.membership_type || '',
        membership_status: user.membership_status || '',
        membership_start_date: user.membership_start_date ? user.membership_start_date.slice(0,10) : '',
        membership_end_date: user.membership_end_date ? user.membership_end_date.slice(0,10) : '',
        loyalty_sessions: (override && override.loyalty_sessions != null) ? override.loyalty_sessions : (user.loyalty_sessions || 0),
        once_off_sessions: user.once_off_sessions || 0,
      }));
    }
  }, [user, override]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    try {
      const updates = {
        membership_type: form.membership_type,
        membership_status: form.membership_status,
        membership_start_date: form.membership_start_date || null,
        membership_end_date: form.membership_end_date || null,
      };
      if (type.includes('loyalty')) {
        updates.loyalty_sessions = form.loyalty_sessions;
      }
      if (type.includes('once-off')) {
        updates.once_off_sessions = form.once_off_sessions;
      }
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);
      if (error) throw error;
      toast.success('Plan updated successfully!');
      setTimeout(() => {
        setSaving(false);
        onSave();
      }, 100);
    } catch (err) {
      toast.error('Failed to update plan: ' + (err.message || err));
      setSaving(false);
    }
  };

  if (!open) return null;
  const type = (form.membership_type || '').toLowerCase();
  const showTypeAndSessions = type.includes('loyalty') || type.includes('once-off');

  return (
    <ModalOverlay>
      <ModalContent>
        <h3>Edit Plan for {user.first_name} {user.last_name}</h3>
        <form onSubmit={handleSubmit}>
          {showTypeAndSessions && (
            <>
              <label>Membership Type:<br/>
                <select value={form.membership_type} onChange={e => setForm(f => ({...f, membership_type: e.target.value}))}>
                  <option value="">None</option>
                  <option value="loyalty">Loyalty</option>
                  <option value="once-off">Once-off</option>
                </select>
              </label><br/>
              {type.includes('loyalty') && (
                <label>Loyalty Sessions:<br/>
                  <input type="number" min="0" max="6" value={form.loyalty_sessions} onChange={e => {
                    let val = +e.target.value;
                    if (val < 0) val = 0;
                    if (val > 6) val = 6;
                    setForm(f => ({...f, loyalty_sessions: val}));
                  }} />
                </label>
              )}
              {type.includes('once-off') && (
                <label>Once-off Sessions:<br/>
                  <input type="number" min="0" value={form.once_off_sessions} onChange={e => {
                    let val = +e.target.value;
                    if (val < 0) val = 0;
                    setForm(f => ({...f, once_off_sessions: val}));
                  }} />
                </label>
              )}
            </>
          )}
          {!showTypeAndSessions && (
            <>
              <label>Status:<br/>
                <select value={form.membership_status} onChange={e => setForm(f => ({...f, membership_status: e.target.value}))}>
                  <option value="inactive">Inactive</option>
                  <option value="active">Active</option>
                </select>
              </label><br/>
              <label>Start Date:<br/>
                <input type="date" value={form.membership_start_date} onChange={e => setForm(f => ({...f, membership_start_date: e.target.value}))} />
              </label><br/>
              <label>End Date:<br/>
                <input type="date" value={form.membership_end_date} onChange={e => setForm(f => ({...f, membership_end_date: e.target.value}))} />
              </label><br/>
            </>
          )}
          <div style={{marginTop: 16, display: 'flex', gap: 8}}>
            <button type="submit" disabled={saving}>Save</button>
            <button type="button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </ModalContent>
    </ModalOverlay>
  );
}

EditPlanModal.propTypes = {
  user: PropTypes.object,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  override: PropTypes.object,
};

const UserRoleManagement = () => {
  const { user: currentUser } = useAuth();
  const { data: users = [], isLoading, isError, error } = useUsers();
  const toggleAdminMutation = useToggleAdmin();
  const toggleCoachMutation = useToggleCoach();
  const deleteUserMutation = useDeleteUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [editUser, setEditUser] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [loyaltySessions, setLoyaltySessions] = useState({});
  const [editFormOverride, setEditFormOverride] = useState(null);

  const handleToggleAdmin = async (userId) => {
    try {
      await toggleAdminMutation.mutateAsync(userId);
      toast.success('User admin status updated successfully!');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error.message || 'Failed to update user admin status');
    }
  };

  const handleToggleCoach = async (userId) => {
    try {
      await toggleCoachMutation.mutateAsync(userId);
      toast.success('User coach status updated successfully!');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error.message || 'Failed to update user coach status');
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      await deleteUserMutation.mutateAsync(userId);
      toast.success('User deleted successfully');
    } catch (error) {
      console.error('Error deleting user:', error);
      // Only show error toast if the error is not a 404 (user already deleted)
      if (!error.message?.includes('404')) {
        toast.error(error.message || 'Failed to delete user');
      }
    }
  };

  // Filter and sort users
  const filteredUsers = useMemo(() => {
    return users
      .filter(user => {
        const searchLower = searchTerm.toLowerCase();
        return (
          user.email.toLowerCase().includes(searchLower) ||
          (user.first_name && user.first_name.toLowerCase().includes(searchLower)) ||
          (user.last_name && user.last_name.toLowerCase().includes(searchLower))
        );
      })
      .sort((a, b) => {
        // Always keep current user at top
        if (a.id === currentUser?.id) return -1;
        if (b.id === currentUser?.id) return 1;

        // Then sort by email
        return a.email.localeCompare(b.email);
      });
  }, [users, searchTerm, currentUser]);

  useEffect(() => {
    async function fetchLoyaltySessions() {
      const results = {};
      for (const user of users) {
        const type = (user.membership_type || '').toLowerCase();
        if (type.includes('loyalty')) {
          // Get latest completed loyalty-card payment
          const { data: payment, error: paymentError } = await supabase
            .from('payments')
            .select('*')
            .eq('user_id', user.id)
            .eq('plan_id', 'loyalty-card')
            .eq('status', 'completed')
            .order('payment_date', { ascending: false })
            .limit(1)
            .single();
          if (payment && !paymentError) {
            console.log('Loyalty payment ID for user', user.email, ':', payment.id);
            const { data: used, error: usedError } = await supabase.rpc('get_used_loyalty_sessions', { payment_id_input: payment.id });
            console.log('Loyalty RPC result:', used);
            let usedCount = 0;
            if (!usedError) {
              if (typeof used === 'number') {
                usedCount = used;
              } else if (Array.isArray(used) && typeof used[0] === 'number') {
                usedCount = used[0];
              }
              results[user.id] = 6 - usedCount;
            } else {
              results[user.id] = 'Err';
            }
          } else {
            results[user.id] = 'N/A';
          }
        } else {
          results[user.id] = 'N/A';
        }
      }
      setLoyaltySessions(results);
    }
    if (users && users.length) fetchLoyaltySessions();
  }, [users]);

  if (isLoading) {
    return (
      <Container>
        <HeaderSection>
          <h2>User Role Management</h2>
          <p>Loading user data...</p>
        </HeaderSection>
        <EmptyState>
          <Spinner />
          <h3>Loading Users</h3>
          <p>Please wait while we fetch the user data...</p>
        </EmptyState>
      </Container>
    );
  }

  if (isError) {
    return (
      <Container>
        <HeaderSection>
          <h2>User Role Management</h2>
          <p>Error occurred while loading data</p>
        </HeaderSection>
        <EmptyState>
          <h3>Error Loading Users</h3>
          <p>Error loading users: {error?.message || 'Something went wrong'}</p>
        </EmptyState>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <HeaderSection>
          <h2>User Role Management</h2>
          <p>Error occurred while loading data</p>
        </HeaderSection>
        <EmptyState>
          <h3>Error Loading Users</h3>
          <p>{error}</p>
        </EmptyState>
      </Container>
    );
  }

  return (
    <Container>
      <HeaderSection>
        <h2>User Role Management</h2>
        <p>Manage user roles, permissions, and membership details</p>
      </HeaderSection>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="🔍 Search users by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {!filteredUsers.length ? (
        <EmptyState>
          <h3>No Users Found</h3>
          <p>No users found matching your search criteria. Try adjusting your search terms.</p>
        </EmptyState>
      ) : (
        <>
          <MobileTable>
            {filteredUsers.map(user => (
              <UserCard key={user.id} isCurrentUser={user.id === currentUser?.id}>
                <div className="user-info">
                  <h3>{user.first_name || 'No Name'} {user.last_name}</h3>
                  <p><strong>Email:</strong> <span className="value">{user.email}</span></p>
                  <p><strong>Plan:</strong> <span className="value">{user.membership_type || 'None'} ({user.membership_status || 'inactive'})</span></p>
                  <p><strong>Start:</strong> <span className="value">{user.membership_start_date ? new Date(user.membership_start_date).toLocaleDateString() : '-'}</span></p>
                  {user.membership_end_date && (
                    <p><strong>End:</strong> <span className="value">{new Date(user.membership_end_date).toLocaleDateString()}</span></p>
                  )}
                  <p><strong>Loyalty Sessions Remaining:</strong> <span className="value">{loyaltySessions[user.id] ?? '...'}</span></p>
                  <p><strong>Admin:</strong> <span className="value">{user.is_admin ? '✅' : '❌'}</span>  <strong>Coach:</strong> <span className="value">{user.is_coach ? '✅' : '❌'}</span></p>
                </div>
                {currentUser?.email !== user.email && (
                  <ButtonContainer>
                    <ActionButton
                      className="admin"
                      onClick={() => handleToggleAdmin(user.id)}
                      disabled={toggleAdminMutation.isPending || user.id === currentUser?.id}
                      isAdmin={user.is_admin}
                    >
                      {toggleAdminMutation.isPending && toggleAdminMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : user.is_admin ? (
                        'Remove Admin'
                      ) : (
                        'Make Admin'
                      )}
                    </ActionButton>
                    <ActionButton
                      className="coach"
                      onClick={() => handleToggleCoach(user.id)}
                      disabled={toggleCoachMutation.isPending || user.id === currentUser?.id}
                      isCoach={user.is_coach}
                    >
                      {toggleCoachMutation.isPending && toggleCoachMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : user.is_coach ? (
                        'Remove Coach'
                      ) : (
                        'Make Coach'
                      )}
                    </ActionButton>
                    <ActionButton
                      className="delete"
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={deleteUserMutation.isPending || user.id === currentUser?.id}
                    >
                      {deleteUserMutation.isPending && deleteUserMutation.variables === user.id ? (
                        <LoadingSpinner />
                      ) : (
                        'Delete'
                      )}
                    </ActionButton>
                    {/* <ActionButton
                      className="edit"
                      onClick={() => {
                        const type = (user.membership_type || '').toLowerCase();
                        if (type.includes('loyalty')) {
                          setEditFormOverride({ loyalty_sessions: loyaltySessions[user.id] });
                        } else {
                          setEditFormOverride(null);
                        }
                        setEditUser(user);
                        setEditModalOpen(true);
                      }}
                    >
                      Edit Plan
                    </ActionButton> */}
                  </ButtonContainer>
                )}
              </UserCard>
            ))}
          </MobileTable>

          <DesktopTable>
            <TableContainer>
              <Table>
                <div className="table-header">
                  <span>Email</span>
                  <span>Name</span>
                  <span>Plan</span>
                  <span>Start</span>
                  <span>End</span>
                  <span>Loyalty Sessions</span>
                  <span>Admin</span>
                  <span>Coach</span>
                  <span>Actions</span>
                </div>
                {filteredUsers.map(user => (
                  <div
                    key={user.id}
                    className="table-row"
                    style={{
                      background: user.id === currentUser?.id ? `${theme.colors.primary}10` : 'inherit',
                    }}
                  >
                    <div>{user.email}</div>
                    <div>{user.first_name} {user.last_name}</div>
                    <div>{user.membership_type || 'None'} ({user.membership_status || 'inactive'})</div>
                    <div>{user.membership_start_date ? new Date(user.membership_start_date).toLocaleDateString() : '-'}</div>
                    <div>{user.membership_end_date ? new Date(user.membership_end_date).toLocaleDateString() : ''}</div>
                    <div>{loyaltySessions[user.id] ?? '...'}</div>
                    <div>{user.is_admin ? 'Yes' : 'No'}</div>
                    <div>{user.is_coach ? 'Yes' : 'No'}</div>
                    <div>
                      {currentUser?.email !== user.email && (
                        <ButtonContainer>
                          <ActionButton
                            className="admin"
                            onClick={() => handleToggleAdmin(user.id)}
                            disabled={toggleAdminMutation.isPending || user.id === currentUser?.id}
                            isAdmin={user.is_admin}
                          >
                            {toggleAdminMutation.isPending && toggleAdminMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : user.is_admin ? (
                              'Remove Admin'
                            ) : (
                              'Make Admin'
                            )}
                          </ActionButton>
                          {user.is_admin && <ActionButton
                            className="coach"
                            onClick={() => handleToggleCoach(user.id)}
                            disabled={!user.is_admin || toggleCoachMutation.isPending || user.id === currentUser?.id}
                            isCoach={user.is_coach}
                          >
                            {toggleCoachMutation.isPending && toggleCoachMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : user.is_coach ? (
                              'Remove Coach'
                            ) : (
                              'Make Coach'
                            )}
                          </ActionButton>}
                          <ActionButton
                            className="delete"
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={deleteUserMutation.isPending || user.id === currentUser?.id}
                          >
                            {deleteUserMutation.isPending && deleteUserMutation.variables === user.id ? (
                              <LoadingSpinner />
                            ) : (
                              'Delete'
                            )}
                          </ActionButton>
                          {/* <ActionButton
                            className="edit"
                            onClick={() => {
                              const type = (user.membership_type || '').toLowerCase();
                              if (type.includes('loyalty')) {
                                setEditFormOverride({ loyalty_sessions: loyaltySessions[user.id] });
                              } else {
                                setEditFormOverride(null);
                              }
                              setEditUser(user);
                              setEditModalOpen(true);
                            }}
                          >
                            Edit Plan
                          </ActionButton> */}
                        </ButtonContainer>
                      )}
                    </div>
                  </div>
                ))}
              </Table>
            </TableContainer>
          </DesktopTable>
          <EditPlanModal
            user={editUser}
            open={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            override={editFormOverride}
            onSave={() => {
              toast.success('Plan updated successfully!');
              setEditModalOpen(false);
              setEditFormOverride(null);
            }}
          />
        </>
      )}
    </Container>
  );
};

export default UserRoleManagement;
