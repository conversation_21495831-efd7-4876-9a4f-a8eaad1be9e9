import { Navigate, useLocation } from 'react-router-dom';
import { useMembership } from '../hooks/useMembership';
import { useAuth } from '../hooks/useAuth';
import Spinner from './Spinner';

const ProtectedRoute = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { membershipDetails, loading: membershipLoading } = useMembership();
  const location = useLocation();

  // Don't check membership for these routes
  const EXEMPT_ROUTES = ['/signup', '/dashboard'];
  const isExemptRoute = EXEMPT_ROUTES.some(route => location.pathname.startsWith(route));

  if (authLoading || membershipLoading) {
    return <Spinner size="large" text="Loading..." />;
  }

  // If not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If membership is expired and not on an exempt route, redirect to dashboard
  // if (membershipDetails?.isExpired && !isExemptRoute) {
  //   return <Navigate to="/dashboard" replace />;
  // }

  return children;
};

export default ProtectedRoute;
