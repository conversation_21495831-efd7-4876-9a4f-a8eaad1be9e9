import { useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';
import { format } from 'date-fns';
import toast from 'react-hot-toast';
import { useMembership } from '../hooks/useMembership';
import LoyaltyCard from './LoyaltyCard';

const Dashboard = () => {
  const { user } = useAuth();
  const { membershipDetails: loyaltyDetails, loading: loyaltyLoading } = useMembership();
  const [membershipDetails, setMembershipDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMembershipDetails = async () => {
      try {
        console.log('Fetching membership details for user:', user);
        // Get profile with membership details
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select(`
            membership_status,
            membership_type,
            membership_start_date,
            membership_end_date,
            payment_status,
            payments (
              plan_title,
              amount,
              period,
              payment_date
            )
          `)
          .eq('id', user.id)
          .single();

        if (profileError) throw profileError;

        // Get the most recent payment
        const { data: latestPayment, error: paymentError } = await supabase
          .from('payments')
          .select('*')
          .eq('user_id', user.id)
          .order('payment_date', { ascending: false })
          .limit(1)
          .single();

        if (paymentError && paymentError.code !== 'PGRST116') { // Ignore "no rows returned" error
          throw paymentError;
        }

        setMembershipDetails({
          ...profile,
          latestPayment
        });

      } catch (error) {
        console.error('Error fetching membership details:', error);
        toast.error('Failed to load membership details');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchMembershipDetails();
    }
  }, [user]);

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMMM d, yyyy');
  };

  if (loading || loyaltyLoading) {
    return <div>Loading membership details...</div>;
  }

  console.log('Rendering Dashboard with:', { 
    user, 
    loyaltyDetails, 
    membershipDetails 
  });

  return (
    <div>
      <h2>Your Membership</h2>
      {membershipDetails ? (
        <>
          <p>Current Plan: {membershipDetails.latestPayment?.plan_title || 'No plan'}</p>
          <p>Status: {membershipDetails.membership_status || 'Inactive'}</p>
          <p>Next Payment: {formatDate(membershipDetails.membership_end_date)}</p>
          <p>Plan Details: {membershipDetails.latestPayment?.amount} ZAR {membershipDetails.latestPayment?.period}</p>
          <p>Last Payment Date: {formatDate(membershipDetails.latestPayment?.payment_date)}</p>
          
          {console.log('User before passing to card:', user)}
          {console.log('Loyalty details before passing to card:', loyaltyDetails)}
          {user && loyaltyDetails && (
            <LoyaltyCard memberDetails={{
              id: user.id,
              name: `${user?.user_metadata?.first_name} ${user?.user_metadata?.last_name}`,
              endDate: loyaltyDetails?.endDate,
              isValid: !loyaltyDetails?.isExpired,
              sessions: loyaltyDetails?.sessions,
              sessionsUsed: loyaltyDetails?.sessionsUsed,
              sessionsRemaining: loyaltyDetails?.sessionsRemaining
            }} />
          )}
        </>
      ) : (
        <p>No membership information found</p>
      )}
    </div>
  );
};

export default Dashboard; 