import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from '../../styles/theme';
import Membership from '../Membership';
import AuthForm from './AuthForm';
import { initPayFastPayment } from '../../services/payFastService';
import { useAuth } from '../../hooks/useAuth';
import { supabase } from '../../lib/supabase';
import Toast from '../Toast';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: ${theme.spacing.xl};
  background: ${theme.colors.background};
`;

const StepperContainer = styled.div`
  width: 100%;
  max-width: 800px;
  margin: ${theme.spacing['2xl']} auto;
`;

const StepIndicator = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: ${theme.spacing['2xl']};
`;

const Step = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;

  &:not(:last-child):after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: ${props => props.$active || props.$completed ? theme.colors.primary : theme.colors.border};
    top: 15px;
    left: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }
`;

const StepWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: ${props => props.$clickable ? 'pointer' : 'default'};
`;

const StepNumber = styled.button`
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: ${props => props.$active ? theme.colors.primary : props.$completed ? theme.colors.success : theme.colors.border};
  color: ${props => props.$active || props.$completed ? 'white' : theme.colors.text};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.sm};
  position: relative;
  z-index: 2;
  border: none;
  padding: 0;
  cursor: ${props => props.$clickable ? 'pointer' : 'default'};
`;

const StepLabel = styled.button`
  font-size: ${theme.fontSizes.sm};
  color: ${props => props.$active ? theme.colors.primary : theme.colors.text};
  font-weight: ${props => props.$active ? theme.fontWeights.bold : theme.fontWeights.normal};
  border: none;
  background: none;
  padding: 0;
  cursor: ${props => props.$clickable ? 'pointer' : 'default'};
`;

const Card = styled.div`
  background: ${theme.colors.background};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.lg};
`;

const SelectButton = styled.button`
  background: ${theme.colors.primary};
  color: ${theme.colors.background};
  border: none;
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.medium};
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;

  &:hover {
    background: ${theme.colors.buttonHover.primary};
  }

  &:disabled {
    background: ${theme.colors.border};
    cursor: not-allowed;
  }
`;

const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const LoadingText = styled.div`
  font-size: ${theme.fontSizes.lg};
  color: ${theme.colors.primary};
  margin-bottom: ${theme.spacing.md};
  text-align: center;
`;

const LoadingSpinner = styled.div`
  border: 4px solid ${theme.colors.border};
  border-top: 4px solid ${theme.colors.primary};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: ${theme.spacing.md};

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Title = styled.h2`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.xl};
  margin-bottom: ${theme.spacing.lg};
`;

const Message = styled.p`
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.md};
`;

const SignupStepper = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [loadingText, setLoadingText] = useState('');
  const [userDetails, setUserDetails] = useState(null);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const { signUp } = useAuth();
  const [registrationLoading, setRegistrationLoading] = useState(false);

  const steps = [
    { number: 1, label: 'Your Details' },
    { number: 2, label: 'Select Plan' },
    { number: 3, label: 'Complete' }
  ];

  const urlStep = searchParams.get('step');
  const paymentStatus = searchParams.get('payment_status');
  const isRenewing = localStorage.getItem('renew') === 'true';
  
  const currentStep = 
    isRenewing ? 2 // Always show plan selection for renewals
    : (urlStep === 'complete' && paymentStatus === 'success') ? 3 
    : urlStep === 'plan' ? 2
    : 1;

  useEffect(() => {
    // If renewing and payment was successful, go straight to dashboard
    if (isRenewing && paymentStatus === 'success') {
      Toast.showSuccess('Payment successful! Your membership has been renewed.');
      localStorage.removeItem('renew'); // Clear renewal flag
      navigate('/dashboard', { replace: true });
      return;
    }

    // Handle normal signup flow
    if (!isRenewing) {
      // If not renewing and trying to access plan step directly, redirect to first step
      if (currentStep === 2 && !sessionStorage.getItem('userDetails')) {
        navigate('/signup', { replace: true });
        return;
      }
      
      const storedPlan = sessionStorage.getItem('selectedPlan');
      const storedDetails = sessionStorage.getItem('userDetails');
      
      if (storedPlan && storedPlan !== "undefined"){
        setSelectedPlan(JSON.parse(storedPlan));
      } 
      if (storedDetails) setUserDetails(JSON.parse(storedDetails));

      // Handle payment success for new signups
      if (urlStep === 'complete' && paymentStatus === 'success') {
        const paymentToastShown = sessionStorage.getItem('paymentToastShown');
        if (!paymentToastShown) {
          Toast.showSuccess('Payment successful! Please complete your account setup.');
          sessionStorage.setItem('paymentToastShown', 'true');
        }
        sessionStorage.setItem('paymentComplete', 'true');
        setPaymentComplete(true);
      }
    }
  }, [currentStep, navigate, urlStep, paymentStatus, isRenewing]);

  const handleStepClick = (stepNumber) => {
    if (paymentComplete || currentStep > stepNumber) {
    switch(stepNumber) {
      case 1:
        navigate('/signup');
        break;
      case 2:
          navigate('/signup?step=plan');
          break;
        default:
          break;
      }
    } else if (stepNumber < currentStep) {
      switch(stepNumber) {
        case 1:
          navigate('/signup');
          break;
        case 2:
          if (userDetails) navigate('/signup?step=plan');
        break;
      default:
        break;
    }
    }
  };

  const handlePlanSelect = async (plan) => {
    try {
      setPaymentLoading(true);
      setLoadingText('Preparing your payment...');

      const storedDetails = sessionStorage.getItem('userDetails');
      if (!storedDetails) throw new Error('User details not found');
      
      const userDetails = JSON.parse(storedDetails);
      setSelectedPlan(plan);
      sessionStorage.setItem('selectedPlan', JSON.stringify(plan));

      const orderDetails = {
        amount: plan.amountInCents / 100,
        description: plan.title
      };

      await initPayFastPayment(orderDetails, userDetails);
      setLoadingText('Redirecting to PayFast...');
    } catch (error) {
      console.error('Payment error:', error);
      Toast.showError(error.message || 'Failed to initiate payment. Please try again.');
      setPaymentLoading(false);
    }
  };

  const handleUserDetailsSubmit = (details) => {
    setUserDetails(details);
    sessionStorage.setItem('userDetails', JSON.stringify(details));
    navigate('/signup?step=plan');
  };

  const handleCompleteRegistration = async () => {
    try {
      console.log('Starting registration process', {
        hasUserDetails: !!userDetails,
        hasSelectedPlan: !!selectedPlan
      });
      
      setRegistrationLoading(true);
      
      if (!userDetails || !selectedPlan) {
        throw new Error('Missing user or plan details');
      }

      // 1. Create user
      console.log('Creating user account');
      const { data: signUpData, error: authError } = await signUp({
        email: userDetails.email,
        password: userDetails.password,
        firstName: userDetails.firstName,
        lastName: userDetails.lastName,
        school: userDetails.school
      });

      if (authError) {
        console.error('Auth error:', authError);
        throw authError;
      }

      // Ensure user creation was successful and we have an initial user object
      if (!signUpData?.user?.id) {
         console.error('User ID not found immediately after signup call');
         throw new Error('User creation failed or did not return a user ID.');
      }
      console.log('Initial user object received from signUp:', signUpData.user.id);

      // Attempt to get the user session/data to ensure consistency
      console.log('Attempting to retrieve user session data...');
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay just in case
      const { data: { user: sessionUser }, error: getUserError } = await supabase.auth.getUser();

      if (getUserError || !sessionUser) {
        console.warn('Failed to get user session immediately after signup, proceeding with signUpData.user.id. Error:', getUserError);
        // Fallback or throw error? For now, proceed cautiously.
        // throw new Error('Could not confirm user session after signup.');
      } else {
        console.log('Confirmed user session retrieved, ID:', sessionUser.id);
        // Optionally, verify sessionUser.id matches signUpData.user.id
        if (sessionUser.id !== signUpData.user.id) {
          console.warn(`User ID mismatch! signUp: ${signUpData.user.id}, getUser: ${sessionUser.id}. Using sessionUser.id.`);
        }
      }

      // Use the most reliable user ID available
      const userIdToUse = sessionUser?.id || signUpData.user.id;
      console.log(`Using User ID: ${userIdToUse} for potential server-side profile creation.`);

      // Always upsert profile after user creation
      let membershipEndDate = null;
      if (selectedPlan.id !== 'loyalty-card' && selectedPlan.id !== 'once-off') {
        membershipEndDate = calculateMembershipEndDate(selectedPlan.period);
      }
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userIdToUse,
          email: userDetails.email,
          first_name: userDetails.firstName,
          last_name: userDetails.lastName,
          school: userDetails.school,
          membership_status: 'active',
          membership_type: selectedPlan.id,
          membership_start_date: new Date().toISOString(),
          membership_end_date: membershipEndDate,
          payment_status: 'completed'
        }, { onConflict: 'id' });
      if (profileError) {
        console.error('Profile upsert error:', profileError);
        throw profileError;
      }

      // 2. Create payment record (Renumbered from 3)
      const paymentData = {
        user_id: userIdToUse, // Use the confirmed user ID
        plan_id: selectedPlan.id,
        plan_title: selectedPlan.title,
        amount: selectedPlan.amountInCents / 100,
        currency: 'ZAR',
        status: 'completed',
        payment_method: 'payfast',
        payment_date: new Date().toISOString(),
        period: selectedPlan.period,
        payment_reference: `${userDetails.firstName}-${userDetails.lastName}-${userDetails.email}`,
        payment_intent_id: `pf_${Date.now()}`
      };

      console.log('Creating payment record');
      const { data: newPayment, error: paymentError } = await supabase
        .from('payments')
        .insert([paymentData])
        .select()
        .single();

      if (paymentError) {
        console.error('Payment record error:', paymentError);
        throw paymentError;
      }

      console.log('Payment record created:', newPayment);

      // Create initial session if this is a loyalty card or once-off payment
      if (selectedPlan.id === 'loyalty-card') {
        // Create 6 pending loyalty sessions
        const sessions = Array(6).fill({
          user_id: userIdToUse,
          payment_id: newPayment.id,
          status: 'pending',
          created_at: new Date().toISOString()
        });

        const { error: sessionsError } = await supabase
          .from('loyalty_sessions')
          .insert(sessions);

        if (sessionsError) {
          console.error('Error creating loyalty sessions:', sessionsError);
          throw sessionsError;
        }

        console.log('Loyalty sessions created');
      } else if (selectedPlan.id === 'once-off') {
        // Create 1 pending once-off session
        const { error: sessionError } = await supabase
          .from('once_off_sessions')
          .insert({
            user_id: userIdToUse,
            payment_id: newPayment.id,
            status: 'pending',
            created_at: new Date().toISOString()
          });

        if (sessionError) {
          console.error('Error creating once-off session:', sessionError);
          throw sessionError;
        }

        console.log('Once-off session created');
      }

      // Membership update logic removed - should be handled server-side

      // Clear storage and show success messages
      sessionStorage.removeItem('userDetails');
      sessionStorage.removeItem('selectedPlan');
      sessionStorage.removeItem('paymentComplete');
      sessionStorage.removeItem('paymentToastShown');

      sessionStorage.setItem('registrationMessage', `Please check ${userDetails.email} for your account activation link.`);
      Toast.showSuccess('Registration completed successfully! Please check your email to activate your account.', {
        duration: 5000,
        icon: '✅'
      });

      console.log('Registration completed, redirecting to login in 5 seconds');
      // Only redirect after successful registration
      setTimeout(() => {
        navigate('/login', { replace: true });
      }, 5000);

    } catch (error) {
      console.error('Registration processing error:', error); // Updated log message
      Toast.showError(error.message || 'Failed to complete registration process');
      // On error, stay on the current page
      setRegistrationLoading(false);
    }
  };

  // Helper function to calculate membership end date
  const calculateMembershipEndDate = (period) => {
    const now = new Date();
    
    if (period === '1 day') {
      // Set to exactly next day at same time
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      // Set the time to match the current time
      tomorrow.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
      return tomorrow.toISOString();
    } 
    
    if (period.includes('quarter') || period === '4 months') {
      const fourMonths = new Date(now);
      fourMonths.setMonth(fourMonths.getMonth() + 4);
      // Keep the same time of day
      fourMonths.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
      return fourMonths.toISOString();
    } 
    
    if (period.includes('year') || period === '12 months') {
      const oneYear = new Date(now);
      oneYear.setMonth(oneYear.getMonth() + 12);
      // Keep the same time of day
      oneYear.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
      return oneYear.toISOString();
    }
    
    return now.toISOString(); // fallback
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            <AuthForm 
              mode="details" 
              onSubmit={handleUserDetailsSubmit} 
              initialValues={userDetails}
              readOnly={paymentComplete || currentStep > 1}
            />
            {(paymentComplete || currentStep > 1) && selectedPlan && (
              <Card style={{ marginTop: theme.spacing.xl }}>
                <Title>Selected Plan</Title>
                <Message>
                  You have selected the {selectedPlan.title} plan at {selectedPlan.price} {selectedPlan.period}.
                </Message>
              </Card>
            )}
          </>
        );
      case 2:
        return (
          <>
            <Membership 
              isSignupFlow={true} 
              onPlanSelect={handlePlanSelect}
              readOnly={paymentComplete}
              selectedPlan={selectedPlan}
            />
            {paymentLoading && (
              <LoadingOverlay>
                <LoadingSpinner />
                <LoadingText>{loadingText}</LoadingText>
                <LoadingText style={{ fontSize: theme.fontSizes.sm }}>
                  Please do not refresh the page
                </LoadingText>
              </LoadingOverlay>
            )}
          </>
        );
      case 3:
        return (
          <>
            {userDetails && (
              <Card style={{ marginBottom: theme.spacing.xl }}>
                <Title>Your Details</Title>
                <Message>
                  Name: {userDetails.firstName} {userDetails.lastName}<br />
                  Email: {userDetails.email}<br />
                  School/Club: {userDetails.school}
                </Message>
              </Card>
            )}
            {selectedPlan && (
              <Card style={{ marginBottom: theme.spacing.xl }}>
                <Title>Selected Plan</Title>
                <Message>
                  {selectedPlan.title}<br />
                  {selectedPlan.price} x {selectedPlan.period}
                </Message>
              </Card>
            )}
            <Card>
              <Title>Payment Successful!</Title>
              <Message>
                Your payment has been processed successfully. Click below to create your account.
              </Message>
              <SelectButton 
                onClick={handleCompleteRegistration}
                disabled={registrationLoading}
              >
                {registrationLoading ? 'Creating Account...' : 'Complete Registration'}
              </SelectButton>
            </Card>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Container>
      <StepperContainer>
        <StepIndicator>
          {steps.map((step) => (
              <Step
                key={step.number}
                $active={currentStep === step.number}
                $completed={currentStep > step.number}
              >
                <StepWrapper
                $clickable={currentStep > step.number || paymentComplete}
                  onClick={() => handleStepClick(step.number)}
                >
                  <StepNumber
                    $active={currentStep === step.number}
                    $completed={currentStep > step.number}
                  $clickable={currentStep > step.number || paymentComplete}
                    onClick={() => handleStepClick(step.number)}
                  >
                    {step.number}
                  </StepNumber>
                  <StepLabel
                    $active={currentStep === step.number}
                  $clickable={currentStep > step.number || paymentComplete}
                    onClick={() => handleStepClick(step.number)}
                  >
                    {step.label}
                  </StepLabel>
                </StepWrapper>
              </Step>
          ))}
        </StepIndicator>
        {renderStep()}
      </StepperContainer>
    </Container>
  );
};

export default SignupStepper;
