import React, { useState, useEffect, useMemo, useRef } from 'react';
import styled from '@emotion/styled';
import { format, isAfter, isBefore, startOfWeek, addWeeks, parseISO, addHours } from 'date-fns';
import Modal from './Modal';
import ClassForm from './ClassForm';
import { toast } from 'react-hot-toast';
import { theme } from '../styles/theme';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';
import PropTypes from 'prop-types';
import { useSessions, useCreateSession, useUpdateSession, useDeleteSession } from '../hooks/useSessionsQuery';

const Container = styled.div`
  padding: ${theme.spacing.md};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }
`;

const TableContainer = styled.div`
  margin-top: ${theme.spacing.lg};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
`;

const Table = styled.div`
  width: 100%;
  min-width: 800px; /* Ensures table doesn't get too squished */

  .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    background: ${theme.colors.background};
    border-bottom: 1px solid ${theme.colors.border};

    span {
      font-weight: ${theme.fontWeights.semibold};
      color: ${theme.colors.secondary};
      font-size: ${theme.fontSizes.sm};
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    border-bottom: 1px solid ${theme.colors.border};
    align-items: center;

    &:hover {
      background: ${theme.colors.background};
    }

    > div {
      font-size: ${theme.fontSizes.sm};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};
    }
  }
`;

const MobileTable = styled.div`
  display: block;

  @media (min-width: ${theme.breakpoints.md}) {
    display: none;
  }
`;

const MobileCard = styled.div`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.md};
  box-shadow: ${theme.shadows.sm};

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: ${theme.spacing.sm};

    h3 {
      font-size: ${theme.fontSizes.md};
      font-weight: ${theme.fontWeights.semibold};
      color: ${theme.colors.secondary};
      margin: 0;
    }
  }

  .card-content {
    display: grid;
    gap: ${theme.spacing.xs};

    .info-row {
      display: grid;
      grid-template-columns: 120px 1fr;
      gap: ${theme.spacing.sm};

      label {
        font-size: ${theme.fontSizes.sm};
        color: ${theme.colors.textLight};
      }

      span {
        font-size: ${theme.fontSizes.sm};
        color: ${theme.colors.text};
      }
    }
  }

  .card-actions {
    display: flex;
    gap: ${theme.spacing.sm};
    margin-top: ${theme.spacing.md};
  }
`;

const DesktopTable = styled.div`
  display: none;

  @media (min-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const ActionButton = styled.button`
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: none;
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;

  &.edit {
    background: ${theme.colors.primary}15;
    color: ${theme.colors.primary};

    &:hover {
      background: ${theme.colors.primary}25;
    }
  }

  &.delete {
    background: ${theme.colors.error}15;
    color: ${theme.colors.error};

    &:hover {
      background: ${theme.colors.error}25;
    }
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.sm} ${theme.spacing.lg};
  }
`;

const BookingButton = styled(ActionButton)`
  font-size: ${theme.fontSizes.xs};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  background-color: ${props => props.isExpanded ? theme.colors.primary : theme.colors.background};
  color: ${props => props.isExpanded ? theme.colors.background : theme.colors.text};
  border: 1px solid ${props => props.isExpanded ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.sm};

  &:hover {
    background-color: ${props => props.isExpanded ? theme.colors.primary : theme.colors.border + '40'};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.sm};
    padding: ${theme.spacing.sm} ${theme.spacing.md};
  }
`;

const ActionBar = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.lg};
  padding: ${theme.spacing.md};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};

  @media (min-width: ${theme.breakpoints.md}) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: ${theme.spacing.lg};
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    grid-template-columns: repeat(4, 1fr);
  }
`;

const StatBox = styled.div`
  padding: ${theme.spacing.md};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};

  h3 {
    font-size: ${theme.fontSizes.sm};
    color: ${theme.colors.textLight};
    margin: 0;
  }

  p {
    font-size: ${theme.fontSizes.lg};
    font-weight: ${theme.fontWeights.bold};
    color: ${theme.colors.text};
    margin: ${theme.spacing.xs} 0 0 0;
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};

    h3 {
      font-size: ${theme.fontSizes.md};
    }

    p {
      font-size: ${theme.fontSizes.xl};
    }
  }
`;

const NoClassesMessage = styled.div`
  text-align: center;
  padding: ${theme.spacing.xl};
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
`;

const ModalContent = styled.div`
  max-height: 80vh;
  overflow-y: auto;
  padding: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }

  /* Custom scrollbar styles */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${theme.colors.background};
    border-radius: ${theme.borderRadius.sm};
  }

  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border};
    border-radius: ${theme.borderRadius.sm};

    &:hover {
      background: ${theme.colors.borderHover};
    }
  }
`;

const BookingsList = styled.div`
  padding: ${theme.spacing.md};
  margin-top: ${theme.spacing.md};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};

  h4 {
    margin: 0 0 ${theme.spacing.md};
    color: ${theme.colors.text};
  }

  .booking-item {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
    gap: ${theme.spacing.sm};
    padding: ${theme.spacing.sm};
    border-bottom: 1px solid ${theme.colors.border};

    &:last-child {
      border-bottom: none;
    }

    div {
      color: ${theme.colors.text};
      font-size: 0.9em;
    }
  }
`;

const LoaderContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: ${theme.spacing.xl};
`;

const Loader = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid ${theme.colors.background};
  border-top: 3px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const AdminClassManagement = ({ view = 'upcoming' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [expandedClass, setExpandedClass] = useState(null);
  const [bookings, setBookings] = useState({});
  const [loadingBookings, setLoadingBookings] = useState({});
  const { user } = useAuth();

  // Use ref to track current expanded class for subscriptions
  const expandedClassRef = useRef(null);

  // Update ref when expandedClass changes
  useEffect(() => {
    expandedClassRef.current = expandedClass;
  }, [expandedClass]);

  // Use React Query to fetch all sessions with bookings
  const {
    data: allSessions = [],
    isLoading: loading,
    refetch: refetchSessions
  } = useSessions({
    startDate: '2020-01-01T00:00:00.000Z', // Far past date to get all sessions
    endDate: '2030-12-31T23:59:59.999Z',   // Far future date to get all sessions
    includeAllSchools: true,
    refetchOnWindowFocus: true,
    refetchInterval: 30000 // Refetch every 30 seconds
  });

  // Process sessions to calculate participant counts from confirmed bookings
  const classes = useMemo(() => {
    return allSessions.map(session => {
      const confirmedBookings = session.bookings?.filter(booking => booking.status === 'confirmed') || [];
      const actualParticipantCount = confirmedBookings.reduce((sum, booking) => sum + (booking.participant_count || 1), 0);

      if (session.id === 'f5202e62-7dbf-4c47-8ff9-95d2e0da30cc') {
        console.log(`DEBUG Session ${session.id} (${session.title}):`, {
          allBookings: session.bookings,
          confirmedBookings,
          actualParticipantCount,
          originalParticipantCount: session.participant_count
        });
      }

      return {
        ...session,
        participant_count: actualParticipantCount,
        profiles: session.coach // Map coach to profiles for compatibility
      };
    });
  }, [allSessions]);

  // React Query mutations
  const createSessionMutation = useCreateSession();
  const updateSessionMutation = useUpdateSession();
  const deleteSessionMutation = useDeleteSession();

  const filteredAndGroupedClasses = useMemo(() => {
    const now = new Date();

    const filtered = classes.filter(cls => {
      const classStartTime = parseISO(cls.start_time);

      if (view === 'upcoming') {
        return isAfter(classStartTime, now);
      } else if (view === 'past') {
        return isBefore(classStartTime, now);
      }
      return true;
    });

    return filtered;
  }, [classes, view]);

  const classStats = useMemo(() => {
    const now = new Date();
    return {
      total: classes.length,
      upcoming: classes.filter(cls => isAfter(parseISO(cls.start_time), now)).length,
      past: classes.filter(cls => isBefore(parseISO(cls.start_time), now)).length
    };
  }, [classes]);

  useEffect(() => {
    // Subscribe to changes in sessions table
    const sessionsSubscription = supabase
      .channel('sessions-changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'sessions'
        },
        (payload) => {
          console.log('Sessions change received:', payload);
          refetchSessions();
        }
      )
      .subscribe();

    // Subscribe to changes in bookings table
    const bookingsSubscription = supabase
      .channel('bookings-changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings'
        },
        (payload) => {
          console.log('Bookings change received:', payload);
          // Always refetch sessions to update participant counts
          refetchSessions();
          // Also fetch expanded bookings if there's an expanded class
          if (expandedClassRef.current) {
            fetchBookings(expandedClassRef.current);
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      sessionsSubscription.unsubscribe();
      bookingsSubscription.unsubscribe();
    };
  }, [refetchSessions]); // Add refetchSessions as dependency

  const handleCreateClass = () => {
    setSelectedClass(null);
    setIsModalOpen(true);
  };

  const handleEditClass = async (classItem) => {
    try {
      // Fetch the full class details including coach profile
      const { data: fullClassDetails, error } = await supabase
        .from('sessions')
        .select(`
          *,
          coach:profiles!coach_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('id', classItem.id)
        .single();

      if (error) throw error;

      // Transform the data to match the form's expected structure
      const classWithCoach = {
        ...fullClassDetails,
        coach_id: fullClassDetails.coach?.id, // Keep just the ID
        coach: fullClassDetails.coach || null, // Keep the full coach object for reference
        type: fullClassDetails.title // Ensure type matches title for the form
      };

      console.log('Editing class with details:', classWithCoach); // For debugging
      setSelectedClass(classWithCoach);
      setIsModalOpen(true);
    } catch (err) {
      console.error('Error fetching class details:', err);
      toast.error('Error loading class details: ' + err.message);
    }
  };

  const handleDeleteClass = async (classId) => {
    try {
      // Use the deleteSessionMutation
      await deleteSessionMutation.mutateAsync(classId);

      toast.success('Class deleted successfully');
      // The React Query mutation will automatically invalidate the cache and trigger a refetch
    } catch (error) {
      console.error('Error deleting class:', error);
      toast.error('Failed to delete class: ' + error.message);
    }
  };

  const handleSubmit = async (data) => {
    try {
      const sessionData = {
        title: data.title,
        type: data.title,
        description: data.description,
        start_time: data.start_time,
        end_time: data.end_time,
        max_participants: parseInt(data.max_participants),
        coach_id: data.coach_id,
        school: data.school
      };

      if (selectedClass) {
        // Use the updateSessionMutation
        await updateSessionMutation.mutateAsync({
          sessionId: selectedClass.id,
          updates: sessionData
        });
      } else {
        // Use the createSessionMutation
        await createSessionMutation.mutateAsync(sessionData);
      }

      toast.success(`Class ${selectedClass ? 'updated' : 'created'} successfully!`);
      setIsModalOpen(false);
      // The React Query mutations will automatically invalidate the cache and trigger a refetch
    } catch (error) {
      console.error('Error saving class:', error);
      toast.error(error.message || 'Failed to save class');
    }
  };

  const handleToggleBookings = async (classId) => {
    if (expandedClass === classId) {
      setExpandedClass(null);
    } else {
      setExpandedClass(classId);
      if (!bookings[classId]) {
        await fetchBookings(classId);
      }
    }
  };

  const fetchBookings = async (classId) => {
    try {
      setLoadingBookings(prev => ({ ...prev, [classId]: true }));

      const { data: bookings, error } = await supabase
        .from('bookings_with_users')
        .select(`
          id,
          created_at,
          user_id,
          participant_count,
          status,
          user_email,
          user_first_name,
          user_last_name
        `)
        .eq('session_id', classId)
        .eq('status', 'confirmed')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Deduplicate by user_id, keeping only the most recent booking per user
      const uniqueBookings = [];
      const seenUsers = new Set();

      for (const booking of bookings) {
        if (!seenUsers.has(booking.user_id)) {
          seenUsers.add(booking.user_id);
          uniqueBookings.push(booking);
        }
      }

      setBookings(prev => ({
        ...prev,
        [classId]: uniqueBookings.map(booking => ({
          ...booking,
          user: {
            email: booking.user_email || `User ${booking.user_id.slice(0,8)}`,
            name: booking.user_first_name && booking.user_last_name
              ? `${booking.user_first_name} ${booking.user_last_name}`
              : 'Unknown'
          }
        }))
      }));
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Error fetching bookings: ' + error.message);
    } finally {
      setLoadingBookings(prev => ({ ...prev, [classId]: false }));
    }
  };

  const renderBookings = (classItem) => {
    if (loadingBookings[classItem.id]) {
      return (
        <LoaderContainer style={{ padding: theme.spacing.md }}>
          <Loader style={{ width: '24px', height: '24px', border: '2px solid' }} />
        </LoaderContainer>
      );
    }

    const classBookings = bookings[classItem.id] || [];
    const totalParticipants = classBookings.reduce((sum, booking) => sum + (booking.participant_count || 1), 0);

    return (
      <BookingsList>
        <h4>Bookings ({totalParticipants})</h4>
        {classBookings.map((booking) => (
          <div key={booking.id} className="booking-item">
            <div>{booking.user.name}</div>
            <div>{booking.user.email}</div>
            <div>{format(parseISO(booking.created_at), 'MMM d, yyyy HH:mm')} ({booking.participant_count || 1})</div>
          </div>
        ))}
      </BookingsList>
    );
  };

  return (
    <Container>
      <ActionBar>
        <StatsContainer>
          <StatBox>
            <h3>Total Classes</h3>
            <p>{classStats.total}</p>
          </StatBox>
          <StatBox>
            <h3>Upcoming</h3>
            <p>{classStats.upcoming}</p>
          </StatBox>
          <StatBox>
            <h3>Past</h3>
            <p>{classStats.past}</p>
          </StatBox>
        </StatsContainer>
        <ActionButton
          className="edit"
          onClick={handleCreateClass}
        >
          Create New Class
        </ActionButton>
      </ActionBar>

      {loading ? (
        <LoaderContainer>
          <Loader />
        </LoaderContainer>
      ) : filteredAndGroupedClasses.length === 0 ? (
        <NoClassesMessage>
          <h3>No classes found</h3>
          <p>There are no {view} classes to display.</p>
        </NoClassesMessage>
      ) : (
        <>
          <MobileTable>
            {filteredAndGroupedClasses.map((classItem) => (
              <React.Fragment key={classItem.id}>
                <MobileCard>
                  <div className="card-header">
                    <h3>{classItem.type}</h3>
                    <span>{format(parseISO(classItem.start_time), 'MMM d, yyyy')}</span>
                  </div>
                  <div className="card-content">
                    <div className="info-row">
                      <label>Time</label>
                      <span>{format(parseISO(classItem.start_time), 'HH:mm')} - {format(parseISO(classItem.end_time), 'HH:mm')}</span>
                    </div>
                    <div className="info-row">
                      <label>Instructor</label>
                      <span>{classItem.profiles?.first_name} {classItem.profiles?.last_name}</span>
                    </div>
                    <div className="info-row">
                      <label>Spots</label>
                      <span>{classItem.participant_count || 0} / {classItem.max_participants}</span>
                    </div>
                  </div>
                  <div className="card-actions">
                    <ActionButton
                      className="edit"
                      onClick={() => handleToggleBookings(classItem.id)}
                    >
                      {expandedClass === classItem.id ? 'Hide' : 'Show'} Bookings
                    </ActionButton>
                    <ActionButton
                      className="edit"
                      onClick={() => handleEditClass(classItem)}
                    >
                      Edit Class
                    </ActionButton>
                    <ActionButton
                      className="delete"
                      onClick={() => {
                        if (window.confirm('Are you sure you want to delete this class?')) {
                          handleDeleteClass(classItem.id);
                        }
                      }}
                    >
                      Delete
                    </ActionButton>
                  </div>
                </MobileCard>
                {expandedClass === classItem.id && (
                  renderBookings(classItem)
                )}
              </React.Fragment>
            ))}
          </MobileTable>

          <DesktopTable>
            <TableContainer>
              <Table>
                <div className="table-header">
                  <span>Class</span>
                  <span>Date</span>
                  <span>Time</span>
                  <span>Spots</span>
                  <span>Actions</span>
                </div>
                {filteredAndGroupedClasses.map((classItem) => (
                  <React.Fragment key={classItem.id}>
                    <div className="table-row">
                      <div>{classItem.type}</div>
                      <div>{format(parseISO(classItem.start_time), 'MMM d, yyyy')}</div>
                      <div>{format(parseISO(classItem.start_time), 'HH:mm')} - {format(parseISO(classItem.end_time), 'HH:mm')}</div>
                      <div>{classItem.participant_count || 0} / {classItem.max_participants}</div>
                      <div>
                        <span>{classItem.profiles?.first_name} {classItem.profiles?.last_name}</span>
                        <BookingButton
                          isExpanded={expandedClass === classItem.id}
                          onClick={() => handleToggleBookings(classItem.id)}
                        >
                          {expandedClass === classItem.id ? 'Hide' : 'Show'} Bookings ({(bookings[classItem.id] || []).length})
                        </BookingButton>
                      </div>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <ActionButton
                          className="edit"
                          onClick={() => handleEditClass(classItem)}
                        >
                          Edit
                        </ActionButton>
                        <ActionButton
                          className="delete"
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this class?')) {
                              handleDeleteClass(classItem.id);
                            }
                          }}
                        >
                          Delete
                        </ActionButton>
                      </div>
                    </div>
                    {expandedClass === classItem.id && (
                      <div className="table-row" style={{ padding: 0, background: 'transparent' }}>
                        <div colSpan="6" style={{ gridColumn: '1 / -1', padding: 0 }}>
                          {renderBookings(classItem)}
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </Table>
            </TableContainer>
          </DesktopTable>
        </>
      )}

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedClass ? 'Edit Class' : 'Create New Class'}
        hideFooter={true}
      >
        <ModalContent>
          <ClassForm
            initialData={selectedClass}
            onSubmit={handleSubmit}
            onCancel={() => setIsModalOpen(false)}
            currentUser={user}
          />
        </ModalContent>
      </Modal>
    </Container>
  );
};

AdminClassManagement.propTypes = {
  view: PropTypes.oneOf(['upcoming', 'past', 'all']).isRequired
};

export default AdminClassManagement;
