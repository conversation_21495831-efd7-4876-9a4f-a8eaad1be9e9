import React, { useState, useEffect, useMemo, useRef } from 'react';
import styled from '@emotion/styled';
import { format, isAfter, isBefore, startOfWeek, addWeeks, parseISO, addHours } from 'date-fns';
import Modal from './Modal';
import ClassForm from './ClassForm';
import { toast } from 'react-hot-toast';
import { theme } from '../styles/theme';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';
import PropTypes from 'prop-types';
import { useSessions, useCreateSession, useUpdateSession, useDeleteSession } from '../hooks/useSessionsQuery';

const Container = styled.div`
  padding: ${theme.spacing.lg};
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};
  }
`;

const TableContainer = styled.div`
  margin-top: ${theme.spacing.lg};
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
`;

const Table = styled.div`
  width: 100%;
  min-width: 800px; /* Ensures table doesn't get too squished */

  .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    background: ${theme.colors.background};
    border-bottom: 1px solid ${theme.colors.border};

    span {
      font-weight: ${theme.fontWeights.semibold};
      color: ${theme.colors.secondary};
      font-size: ${theme.fontSizes.sm};
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    padding: ${theme.spacing.sm} ${theme.spacing.md};
    border-bottom: 1px solid ${theme.colors.border};
    align-items: center;

    &:hover {
      background: ${theme.colors.background};
    }

    > div {
      font-size: ${theme.fontSizes.sm};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};
    }
  }
`;

const MobileTable = styled.div`
  display: block;

  @media (min-width: ${theme.breakpoints.md}) {
    display: none;
  }
`;

const MobileCard = styled.div`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.lg};
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: ${theme.spacing.lg};
    padding-bottom: ${theme.spacing.md};
    border-bottom: 1px solid ${theme.colors.border};

    h3 {
      font-size: ${theme.fontSizes.lg};
      font-weight: ${theme.fontWeights.bold};
      color: ${theme.colors.primary};
      margin: 0;
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};

      &::before {
        content: '📅';
        font-size: ${theme.fontSizes.md};
      }
    }

    .date-badge {
      background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
      color: white;
      padding: ${theme.spacing.xs} ${theme.spacing.sm};
      border-radius: ${theme.borderRadius.full};
      font-size: ${theme.fontSizes.xs};
      font-weight: ${theme.fontWeights.semibold};
      white-space: nowrap;
    }
  }

  .card-content {
    display: grid;
    gap: ${theme.spacing.md};

    .info-row {
      display: grid;
      grid-template-columns: 100px 1fr;
      gap: ${theme.spacing.md};
      align-items: center;
      padding: ${theme.spacing.sm};
      border-radius: ${theme.borderRadius.sm};
      background: rgba(248, 250, 252, 0.5);

      label {
        font-size: ${theme.fontSizes.sm};
        color: ${theme.colors.textLight};
        font-weight: ${theme.fontWeights.medium};
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        font-size: ${theme.fontSizes.sm};
        color: ${theme.colors.text};
        font-weight: ${theme.fontWeights.medium};
      }

      &.spots {
        .spots-indicator {
          display: flex;
          align-items: center;
          gap: ${theme.spacing.sm};

          .spots-text {
            font-weight: ${theme.fontWeights.bold};
            color: ${theme.colors.primary};
          }

          .spots-bar {
            flex: 1;
            height: 6px;
            background: ${theme.colors.border};
            border-radius: ${theme.borderRadius.full};
            overflow: hidden;

            .spots-fill {
              height: 100%;
              background: linear-gradient(90deg, ${theme.colors.success}, ${theme.colors.primary});
              border-radius: ${theme.borderRadius.full};
              transition: width 0.3s ease;
            }
          }
        }
      }
    }
  }

  .card-actions {
    display: flex;
    gap: ${theme.spacing.sm};
    margin-top: ${theme.spacing.lg};
    padding-top: ${theme.spacing.md};
    border-top: 1px solid ${theme.colors.border};
  }
`;

const DesktopTable = styled.div`
  display: none;

  @media (min-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const ActionButton = styled.button`
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: none;
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  min-width: 100px;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &.edit {
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &::after {
      content: '✏️';
      margin-left: ${theme.spacing.xs};
    }
  }

  &.delete {
    background: linear-gradient(135deg, ${theme.colors.error}, #dc2626);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
    }

    &::after {
      content: '🗑️';
      margin-left: ${theme.spacing.xs};
    }
  }

  &.bookings {
    background: linear-gradient(135deg, ${theme.colors.success}, #059669);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
    }

    &::after {
      content: '👥';
      margin-left: ${theme.spacing.xs};
    }
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.sm} ${theme.spacing.lg};
    min-width: 120px;
  }
`;

const BookingButton = styled(ActionButton)`
  font-size: ${theme.fontSizes.xs};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  background-color: ${props => props.isExpanded ? theme.colors.primary : theme.colors.background};
  color: ${props => props.isExpanded ? theme.colors.background : theme.colors.text};
  border: 1px solid ${props => props.isExpanded ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.sm};

  &:hover {
    background-color: ${props => props.isExpanded ? theme.colors.primary : theme.colors.border + '40'};
  }

  @media (min-width: ${theme.breakpoints.md}) {
    font-size: ${theme.fontSizes.sm};
    padding: ${theme.spacing.sm} ${theme.spacing.md};
  }
`;

const ActionBar = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.xl};
  padding: ${theme.spacing.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  @media (min-width: ${theme.breakpoints.md}) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: ${theme.spacing.xl};
  }

  .action-bar-header {
    display: flex;
    align-items: center;
    gap: ${theme.spacing.md};
    margin-bottom: ${theme.spacing.md};

    h1 {
      font-size: ${theme.fontSizes.xl};
      font-weight: ${theme.fontWeights.bold};
      color: ${theme.colors.primary};
      margin: 0;
      display: flex;
      align-items: center;
      gap: ${theme.spacing.sm};

      &::before {
        content: '🎾';
        font-size: ${theme.fontSizes.lg};
      }
    }

    @media (min-width: ${theme.breakpoints.md}) {
      margin-bottom: 0;
    }
  }

  .create-button {
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    color: white;
    padding: ${theme.spacing.md} ${theme.spacing.lg};
    border: none;
    border-radius: ${theme.borderRadius.lg};
    font-size: ${theme.fontSizes.md};
    font-weight: ${theme.fontWeights.bold};
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};
    min-width: 180px;
    justify-content: center;

    &::before {
      content: '➕';
      font-size: ${theme.fontSizes.md};
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    grid-template-columns: repeat(4, 1fr);
  }
`;

const StatBox = styled.div`
  padding: ${theme.spacing.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary}, ${theme.colors.success});
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  h3 {
    font-size: ${theme.fontSizes.sm};
    color: ${theme.colors.textLight};
    margin: 0 0 ${theme.spacing.sm} 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: ${theme.fontWeights.semibold};
    display: flex;
    align-items: center;
    gap: ${theme.spacing.xs};
  }

  p {
    font-size: ${theme.fontSizes.xl};
    font-weight: ${theme.fontWeights.bold};
    color: ${theme.colors.primary};
    margin: 0;
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};
  }

  &:nth-child(1) {
    h3::before { content: '📊'; }
  }

  &:nth-child(2) {
    h3::before { content: '⏰'; }
  }

  &:nth-child(3) {
    h3::before { content: '✅'; }
  }

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.xl};

    h3 {
      font-size: ${theme.fontSizes.md};
    }

    p {
      font-size: ${theme.fontSizes.xxl};
    }
  }
`;

const NoClassesMessage = styled.div`
  text-align: center;
  padding: ${theme.spacing.xxl};
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  h3 {
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.xl};
    margin: 0 0 ${theme.spacing.md};
    display: flex;
    align-items: center;
    justify-content: center;
    gap: ${theme.spacing.sm};

    &::before {
      content: '📅';
      font-size: ${theme.fontSizes.lg};
    }
  }

  p {
    margin: 0;
    font-size: ${theme.fontSizes.md};
    color: ${theme.colors.textLight};
  }
`;

const ModalContent = styled.div`
  max-height: 80vh;
  overflow-y: auto;
  padding: ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing.lg};
  }

  /* Custom scrollbar styles */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${theme.colors.background};
    border-radius: ${theme.borderRadius.sm};
  }

  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border};
    border-radius: ${theme.borderRadius.sm};

    &:hover {
      background: ${theme.colors.borderHover};
    }
  }
`;

const BookingsList = styled.div`
  padding: ${theme.spacing.lg};
  margin-top: ${theme.spacing.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  h4 {
    margin: 0 0 ${theme.spacing.lg};
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.lg};
    font-weight: ${theme.fontWeights.bold};
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};

    &::before {
      content: '👥';
      font-size: ${theme.fontSizes.md};
    }
  }

  .booking-item {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
    gap: ${theme.spacing.md};
    padding: ${theme.spacing.md};
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: ${theme.borderRadius.sm};
    margin-bottom: ${theme.spacing.sm};
    background: rgba(248, 250, 252, 0.5);
    transition: all 0.2s ease;

    &:hover {
      background: rgba(248, 250, 252, 0.8);
      transform: translateX(4px);
    }

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    div {
      color: ${theme.colors.text};
      font-size: ${theme.fontSizes.sm};
      font-weight: ${theme.fontWeights.medium};
      display: flex;
      align-items: center;
      gap: ${theme.spacing.xs};

      &:first-child {
        color: ${theme.colors.primary};
        font-weight: ${theme.fontWeights.semibold};

        &::before {
          content: '👤';
          font-size: ${theme.fontSizes.xs};
        }
      }

      &:nth-child(2) {
        color: ${theme.colors.textLight};

        &::before {
          content: '📧';
          font-size: ${theme.fontSizes.xs};
        }
      }

      &:last-child {
        color: ${theme.colors.secondary};
        font-weight: ${theme.fontWeights.semibold};
        justify-self: end;

        &::before {
          content: '📅';
          font-size: ${theme.fontSizes.xs};
        }
      }
    }
  }

  .no-bookings {
    text-align: center;
    padding: ${theme.spacing.xl};
    color: ${theme.colors.textLight};
    font-style: italic;

    &::before {
      content: '📭';
      display: block;
      font-size: ${theme.fontSizes.xl};
      margin-bottom: ${theme.spacing.md};
    }
  }
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: ${theme.spacing.xxl};
  min-height: 300px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  .loading-text {
    margin-top: ${theme.spacing.md};
    color: ${theme.colors.primary};
    font-size: ${theme.fontSizes.md};
    font-weight: ${theme.fontWeights.medium};
    display: flex;
    align-items: center;
    gap: ${theme.spacing.sm};

    &::before {
      content: '⏳';
      font-size: ${theme.fontSizes.lg};
    }
  }
`;

const Loader = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid ${theme.colors.background};
  border-top: 3px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const AdminClassManagement = ({ view = 'upcoming' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [expandedClass, setExpandedClass] = useState(null);
  const [bookings, setBookings] = useState({});
  const [loadingBookings, setLoadingBookings] = useState({});
  const { user } = useAuth();

  // Use ref to track current expanded class for subscriptions
  const expandedClassRef = useRef(null);

  // Update ref when expandedClass changes
  useEffect(() => {
    expandedClassRef.current = expandedClass;
  }, [expandedClass]);

  // Use React Query to fetch all sessions with bookings
  const {
    data: allSessions = [],
    isLoading: loading,
    refetch: refetchSessions
  } = useSessions({
    startDate: '2020-01-01T00:00:00.000Z', // Far past date to get all sessions
    endDate: '2030-12-31T23:59:59.999Z',   // Far future date to get all sessions
    includeAllSchools: true,
    refetchOnWindowFocus: true,
    refetchInterval: 30000 // Refetch every 30 seconds
  });

  // Process sessions to calculate participant counts from confirmed bookings
  const classes = useMemo(() => {
    return allSessions.map(session => {
      const confirmedBookings = session.bookings?.filter(booking => booking.status === 'confirmed') || [];
      const actualParticipantCount = confirmedBookings.reduce((sum, booking) => sum + (booking.participant_count || 1), 0);

      return {
        ...session,
        participant_count: actualParticipantCount,
        profiles: session.coach // Map coach to profiles for compatibility
      };
    });
  }, [allSessions]);

  // React Query mutations
  const createSessionMutation = useCreateSession();
  const updateSessionMutation = useUpdateSession();
  const deleteSessionMutation = useDeleteSession();

  const filteredAndGroupedClasses = useMemo(() => {
    const now = new Date();

    const filtered = classes.filter(cls => {
      const classStartTime = parseISO(cls.start_time);

      if (view === 'upcoming') {
        return isAfter(classStartTime, now);
      } else if (view === 'past') {
        return isBefore(classStartTime, now);
      }
      return true;
    });

    return filtered;
  }, [classes, view]);

  const classStats = useMemo(() => {
    const now = new Date();
    return {
      total: classes.length,
      upcoming: classes.filter(cls => isAfter(parseISO(cls.start_time), now)).length,
      past: classes.filter(cls => isBefore(parseISO(cls.start_time), now)).length
    };
  }, [classes]);

  useEffect(() => {
    // Subscribe to changes in sessions table
    const sessionsSubscription = supabase
      .channel('sessions-changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'sessions'
        },
        (payload) => {
          console.log('Sessions change received:', payload);
          refetchSessions();
        }
      )
      .subscribe();

    // Subscribe to changes in bookings table
    const bookingsSubscription = supabase
      .channel('bookings-changes')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings'
        },
        (payload) => {
          console.log('Bookings change received:', payload);
          // Always refetch sessions to update participant counts
          refetchSessions();
          // Also fetch expanded bookings if there's an expanded class
          if (expandedClassRef.current) {
            fetchBookings(expandedClassRef.current);
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      sessionsSubscription.unsubscribe();
      bookingsSubscription.unsubscribe();
    };
  }, [refetchSessions]); // Add refetchSessions as dependency

  const handleCreateClass = () => {
    setSelectedClass(null);
    setIsModalOpen(true);
  };

  const handleEditClass = async (classItem) => {
    try {
      // Fetch the full class details including coach profile
      const { data: fullClassDetails, error } = await supabase
        .from('sessions')
        .select(`
          *,
          coach:profiles!coach_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('id', classItem.id)
        .single();

      if (error) throw error;

      // Transform the data to match the form's expected structure
      const classWithCoach = {
        ...fullClassDetails,
        coach_id: fullClassDetails.coach?.id, // Keep just the ID
        coach: fullClassDetails.coach || null, // Keep the full coach object for reference
        type: fullClassDetails.title // Ensure type matches title for the form
      };

      console.log('Editing class with details:', classWithCoach); // For debugging
      setSelectedClass(classWithCoach);
      setIsModalOpen(true);
    } catch (err) {
      console.error('Error fetching class details:', err);
      toast.error('Error loading class details: ' + err.message);
    }
  };

  const handleDeleteClass = async (classId) => {
    try {
      // Use the deleteSessionMutation
      await deleteSessionMutation.mutateAsync(classId);

      toast.success('Class deleted successfully');
      // The React Query mutation will automatically invalidate the cache and trigger a refetch
    } catch (error) {
      console.error('Error deleting class:', error);
      toast.error('Failed to delete class: ' + error.message);
    }
  };

  const handleSubmit = async (data) => {
    try {
      const sessionData = {
        title: data.title,
        type: data.title,
        description: data.description,
        start_time: data.start_time,
        end_time: data.end_time,
        max_participants: parseInt(data.max_participants),
        coach_id: data.coach_id,
        school: data.school
      };

      if (selectedClass) {
        // Use the updateSessionMutation
        await updateSessionMutation.mutateAsync({
          sessionId: selectedClass.id,
          updates: sessionData
        });
      } else {
        // Use the createSessionMutation
        await createSessionMutation.mutateAsync(sessionData);
      }

      toast.success(`Class ${selectedClass ? 'updated' : 'created'} successfully!`);
      setIsModalOpen(false);
      // The React Query mutations will automatically invalidate the cache and trigger a refetch
    } catch (error) {
      console.error('Error saving class:', error);
      toast.error(error.message || 'Failed to save class');
    }
  };

  const handleToggleBookings = async (classId) => {
    if (expandedClass === classId) {
      setExpandedClass(null);
    } else {
      setExpandedClass(classId);
      if (!bookings[classId]) {
        await fetchBookings(classId);
      }
    }
  };

  const fetchBookings = async (classId) => {
    try {
      setLoadingBookings(prev => ({ ...prev, [classId]: true }));

      const { data: bookings, error } = await supabase
        .from('bookings_with_users')
        .select(`
          id,
          created_at,
          user_id,
          participant_count,
          status,
          user_email,
          user_first_name,
          user_last_name
        `)
        .eq('session_id', classId)
        .eq('status', 'confirmed')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Deduplicate by user_id, keeping only the most recent booking per user
      const uniqueBookings = [];
      const seenUsers = new Set();

      for (const booking of bookings) {
        if (!seenUsers.has(booking.user_id)) {
          seenUsers.add(booking.user_id);
          uniqueBookings.push(booking);
        }
      }

      setBookings(prev => ({
        ...prev,
        [classId]: uniqueBookings.map(booking => ({
          ...booking,
          user: {
            email: booking.user_email || `User ${booking.user_id.slice(0,8)}`,
            name: booking.user_first_name && booking.user_last_name
              ? `${booking.user_first_name} ${booking.user_last_name}`
              : 'Unknown'
          }
        }))
      }));
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Error fetching bookings: ' + error.message);
    } finally {
      setLoadingBookings(prev => ({ ...prev, [classId]: false }));
    }
  };

  const renderBookings = (classItem) => {
    if (loadingBookings[classItem.id]) {
      return (
        <LoaderContainer style={{ padding: theme.spacing.md }}>
          <Loader style={{ width: '24px', height: '24px', border: '2px solid' }} />
        </LoaderContainer>
      );
    }

    const classBookings = bookings[classItem.id] || [];
    const totalParticipants = classBookings.reduce((sum, booking) => sum + (booking.participant_count || 1), 0);

    return (
      <BookingsList>
        <h4>Bookings ({totalParticipants} participants)</h4>
        {classBookings.length > 0 ? (
          classBookings.map((booking) => (
            <div key={booking.id} className="booking-item">
              <div>{booking.user.name}</div>
              <div>{booking.user.email}</div>
              <div>{format(parseISO(booking.created_at), 'MMM d, HH:mm')} ({booking.participant_count || 1})</div>
            </div>
          ))
        ) : (
          <div className="no-bookings">
            No bookings yet for this class
          </div>
        )}
      </BookingsList>
    );
  };

  return (
    <Container>
      <ActionBar>
        <div className="action-bar-header">
          <h1>Class Management</h1>
        </div>
        <StatsContainer>
          <StatBox>
            <h3>Total Classes</h3>
            <p>{classStats.total}</p>
          </StatBox>
          <StatBox>
            <h3>Upcoming</h3>
            <p>{classStats.upcoming}</p>
          </StatBox>
          <StatBox>
            <h3>Past</h3>
            <p>{classStats.past}</p>
          </StatBox>
        </StatsContainer>
        <button
          className="create-button"
          onClick={handleCreateClass}
        >
          Create New Class
        </button>
      </ActionBar>

      {loading ? (
        <LoaderContainer>
          <Loader />
          <div className="loading-text">Loading classes...</div>
        </LoaderContainer>
      ) : filteredAndGroupedClasses.length === 0 ? (
        <NoClassesMessage>
          <h3>No classes found</h3>
          <p>There are no {view} classes to display.</p>
        </NoClassesMessage>
      ) : (
        <>
          <MobileTable>
            {filteredAndGroupedClasses.map((classItem) => (
              <React.Fragment key={classItem.id}>
                <MobileCard>
                  <div className="card-header">
                    <h3>{classItem.type}</h3>
                    <div className="date-badge">{format(parseISO(classItem.start_time), 'MMM d, yyyy')}</div>
                  </div>
                  <div className="card-content">
                    <div className="info-row">
                      <label>⏰ Time</label>
                      <span>{format(parseISO(classItem.start_time), 'HH:mm')} - {format(parseISO(classItem.end_time), 'HH:mm')}</span>
                    </div>
                    <div className="info-row">
                      <label>👨‍🏫 Coach</label>
                      <span>{classItem.profiles?.first_name} {classItem.profiles?.last_name}</span>
                    </div>
                    <div className="info-row spots">
                      <label>👥 Spots</label>
                      <div className="spots-indicator">
                        <span className="spots-text">{classItem.participant_count || 0} / {classItem.max_participants}</span>
                        <div className="spots-bar">
                          <div
                            className="spots-fill"
                            style={{
                              width: `${((classItem.participant_count || 0) / classItem.max_participants) * 100}%`
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="card-actions">
                    <ActionButton
                      className="bookings"
                      onClick={() => handleToggleBookings(classItem.id)}
                    >
                      {expandedClass === classItem.id ? 'Hide' : 'Show'} Bookings
                    </ActionButton>
                    <ActionButton
                      className="edit"
                      onClick={() => handleEditClass(classItem)}
                    >
                      Edit
                    </ActionButton>
                    <ActionButton
                      className="delete"
                      onClick={() => {
                        if (window.confirm('Are you sure you want to delete this class?')) {
                          handleDeleteClass(classItem.id);
                        }
                      }}
                    >
                      Delete
                    </ActionButton>
                  </div>
                </MobileCard>
                {expandedClass === classItem.id && (
                  renderBookings(classItem)
                )}
              </React.Fragment>
            ))}
          </MobileTable>

          <DesktopTable>
            <TableContainer>
              <Table>
                <div className="table-header">
                  <span>Class</span>
                  <span>Date</span>
                  <span>Time</span>
                  <span>Spots</span>
                  <span>Actions</span>
                </div>
                {filteredAndGroupedClasses.map((classItem) => (
                  <React.Fragment key={classItem.id}>
                    <div className="table-row">
                      <div>{classItem.type}</div>
                      <div>{format(parseISO(classItem.start_time), 'MMM d, yyyy')}</div>
                      <div>{format(parseISO(classItem.start_time), 'HH:mm')} - {format(parseISO(classItem.end_time), 'HH:mm')}</div>
                      <div>{classItem.participant_count || 0} / {classItem.max_participants}</div>
                      <div>
                        <span>{classItem.profiles?.first_name} {classItem.profiles?.last_name}</span>
                        <BookingButton
                          isExpanded={expandedClass === classItem.id}
                          onClick={() => handleToggleBookings(classItem.id)}
                        >
                          {expandedClass === classItem.id ? 'Hide' : 'Show'} Bookings ({(bookings[classItem.id] || []).length})
                        </BookingButton>
                      </div>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <ActionButton
                          className="edit"
                          onClick={() => handleEditClass(classItem)}
                        >
                          Edit
                        </ActionButton>
                        <ActionButton
                          className="delete"
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this class?')) {
                              handleDeleteClass(classItem.id);
                            }
                          }}
                        >
                          Delete
                        </ActionButton>
                      </div>
                    </div>
                    {expandedClass === classItem.id && (
                      <div className="table-row" style={{ padding: 0, background: 'transparent' }}>
                        <div colSpan="6" style={{ gridColumn: '1 / -1', padding: 0 }}>
                          {renderBookings(classItem)}
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </Table>
            </TableContainer>
          </DesktopTable>
        </>
      )}

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedClass ? 'Edit Class' : 'Create New Class'}
        hideFooter={true}
      >
        <ModalContent>
          <ClassForm
            initialData={selectedClass}
            onSubmit={handleSubmit}
            onCancel={() => setIsModalOpen(false)}
            currentUser={user}
          />
        </ModalContent>
      </Modal>
    </Container>
  );
};

AdminClassManagement.propTypes = {
  view: PropTypes.oneOf(['upcoming', 'past', 'all']).isRequired
};

export default AdminClassManagement;
