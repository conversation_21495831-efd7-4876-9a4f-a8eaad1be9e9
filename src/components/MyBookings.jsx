import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { theme } from '../styles/theme';
import { format, parseISO, isBefore, differenceInHours } from 'date-fns';
import Modal from './Modal';
import { toast } from 'react-hot-toast';
import { useBookings, useDeleteBooking } from '../hooks/useBookings';
import { useAuth } from '../hooks/useAuth';
import Spinner from './Spinner';
import { supabase } from '../lib/supabase';
import { useQueryClient } from '@tanstack/react-query';
import { canCancelBooking } from '../utils/bookingUtils';

const BookingsContainer = styled.div`
  margin-top: ${theme.spacing['2xl']};
  padding-bottom: ${theme.spacing['2xl']};
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xl};
`;

const DateGroup = styled.div`
  margin-bottom: ${theme.spacing.xl};

  &:last-child {
    margin-bottom: 0;
  }
`;

const DateHeader = styled.h2`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.secondary};
  margin-bottom: ${theme.spacing.lg};
  padding-bottom: ${theme.spacing.sm};
  border-bottom: 2px solid ${theme.colors.border};
`;

const BookingCard = styled.div`
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  padding: ${theme.spacing.lg};
  border: 2px solid ${theme.colors.border};
  margin-bottom: ${theme.spacing.md};
  flex: 1 1 300px;
  max-width: calc(33.333% - ${theme.spacing.xl});
  min-width: 300px;
  opacity: ${props => props.$isPast ? 0.7 : 1};
  transition: all 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.lg};
  }

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    max-width: 100%;
  }
`;

const TimeInfo = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.md};
  background: ${theme.colors.background};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  border: 1px solid ${theme.colors.border};

  span {
    font-size: ${theme.fontSizes.lg};
    color: ${theme.colors.secondary};
    font-weight: ${theme.fontWeights.semibold};
  }
`;

const Title = styled.h3`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.secondary};
  margin-bottom: ${theme.spacing.sm};
`;

const Info = styled.p`
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  margin-bottom: ${theme.spacing.sm};

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-left: ${theme.spacing.sm};
  padding: ${theme.spacing.xs} ${theme.spacing.md};
  background: ${props => props.$type === 'success' ? `${theme.colors.primary}15` :
    props.$type === 'warning' ? `${theme.colors.accent}15` :
    props.$type === 'error' ? `${theme.colors.error}15` :
    `${theme.colors.textLight}15`};
  color: ${props => props.$type === 'success' ? theme.colors.primary :
    props.$type === 'warning' ? theme.colors.accent :
    props.$type === 'error' ? theme.colors.error :
    theme.colors.textLight};
  border: 1px solid ${props => props.$type === 'success' ? theme.colors.primary :
    props.$type === 'warning' ? theme.colors.accent :
    props.$type === 'error' ? theme.colors.error :
    theme.colors.textLight}25;
  border-radius: ${theme.borderRadius.full};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.semibold};
  min-width: 80px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
`;

const CancelButton = styled.button`
  width: 100%;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  background: ${theme.colors.error};
  color: ${theme.colors.background};
  border: none;
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.2s;
  margin-top: ${theme.spacing.md};

  &:hover:not(:disabled) {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing['3xl']};
  color: ${theme.colors.textLight};
  font-size: ${theme.fontSizes.lg};
  background: ${theme.colors.background};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  border: 2px dashed ${theme.colors.border};
  grid-column: 1 / -1;
`;

const TabContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.xl};
  padding: 0 ${theme.spacing.xl};
`;

const Tab = styled.button`
  padding: ${theme.spacing.md} ${theme.spacing.xl};
  background: ${props => props.$active ? theme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : theme.colors.text};
  border: 1px solid ${props => props.$active ? theme.colors.primary : theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  cursor: pointer;
  font-weight: ${theme.fontWeights.medium};
  transition: all 0.2s;

  &:hover {
    background: ${props => props.$active ? theme.colors.primary : theme.colors.background};
    border-color: ${theme.colors.primary};
  }
`;

const MyBookings = () => {
  const { user } = useAuth();
  const { data: bookings, isLoading, isError, error } = useBookings(user?.id);
  const deleteBookingMutation = useDeleteBooking();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [activeTab, setActiveTab] = useState('upcoming');
  const [isCancelling, setIsCancelling] = useState(false);
  const queryClient = useQueryClient();

  const handleCancelClick = (booking) => {
    setSelectedBooking(booking);
    setShowCancelModal(true);
  };

  const handleCancelConfirm = async () => {
    if (!selectedBooking) return;

    try {
      setIsCancelling(true);

      // Delete the booking
      await deleteBookingMutation.mutateAsync(selectedBooking.id);

      // Return loyalty session if it exists
      const { data: loyaltySession, error: loyaltyError } = await supabase
        .from('loyalty_sessions')
        .select('*')
        .eq('booking_id', selectedBooking.id)
        .single();

      if (loyaltyError && loyaltyError.code !== 'PGRST116') { // Ignore "no rows returned" error
        throw loyaltyError;
      }

      if (loyaltySession) {
        // Update the loyalty session status to 'pending'
        const { error: updateError } = await supabase
          .from('loyalty_sessions')
          .update({
            status: 'pending',
            booking_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', loyaltySession.id);

        if (updateError) throw updateError;

        // Get updated loyalty card status
        const { data: cardStatus, error: cardError } = await supabase
          .rpc('get_loyalty_card_status', { p_user_id: user.id });

        if (cardError) throw cardError;

        if (cardStatus && cardStatus.length > 0) {
          toast.success(`Booking cancelled and loyalty session returned. ${cardStatus[0].sessions_remaining} sessions remaining.`);
        } else {
          toast.success('Booking cancelled and loyalty session returned');
        }
      } else {
        toast.success('Booking cancelled successfully');
      }

      // Invalidate all relevant queries
      queryClient.invalidateQueries(['bookings', user?.id]);
      queryClient.invalidateQueries(['loyalty_sessions', user?.id]);
      queryClient.invalidateQueries(['loyalty_card_status', user?.id]);
      queryClient.invalidateQueries(['membership', user?.id]);
      queryClient.invalidateQueries(['profile', user?.id]);

      setShowCancelModal(false);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error('Failed to cancel booking');
    } finally {
      setIsCancelling(false);
      setSelectedBooking(null);
    }
  };

  const getBookingStatus = (booking) => {
    if (!booking || !booking.session) return 'cancelled';
    if (booking.status === 'cancelled') return 'cancelled';
    if (isBefore(parseISO(booking.session.start_time), new Date())) return 'past';
    return 'confirmed';
  };

  const filteredBookings = useMemo(() => {
    if (!bookings) return [];
    return bookings
      .filter(booking => {
        if (!booking || !booking.session) return false;
        const status = getBookingStatus(booking);
        if (activeTab === 'upcoming') {
          return status === 'confirmed';
        } else {
          return status === 'past' || status === 'cancelled';
        }
      })
      .sort((a, b) => {
        return parseISO(a.session.start_time) - parseISO(b.session.start_time);
      });
  }, [bookings, activeTab]);

  const groupedBookings = useMemo(() => {
    const groups = {};
    filteredBookings.forEach(booking => {
      const date = format(parseISO(booking.session.start_time), 'yyyy-MM-dd');
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(booking);
    });
    return groups;
  }, [filteredBookings]);

  if (isLoading) {
    return (
      <BookingsContainer>
        <Spinner />
      </BookingsContainer>
    );
  }

  if (isError) {
    return (
      <BookingsContainer>
        <EmptyState>
          <h3>Error loading bookings</h3>
          <p>{error?.message || 'Something went wrong'}</p>
        </EmptyState>
      </BookingsContainer>
    );
  }

  if (!bookings || bookings.length === 0) {
    return (
      <BookingsContainer>
        <EmptyState>
          <h3>No bookings found</h3>
          <p>Book a session to get started!</p>
        </EmptyState>
      </BookingsContainer>
    );
  }

  return (
    <BookingsContainer>
      <TabContainer>
        <Tab
          $active={activeTab === 'upcoming'}
          onClick={() => setActiveTab('upcoming')}
        >
          Upcoming Sessions
        </Tab>
        <Tab
          $active={activeTab === 'past'}
          onClick={() => setActiveTab('past')}
        >
          Past Sessions
        </Tab>
      </TabContainer>
      {Object.keys(groupedBookings).length === 0 ? (
        <EmptyState>
          <h3>No {activeTab === 'upcoming' ? 'upcoming' : 'past'} bookings</h3>
          {activeTab === 'upcoming' && <p>Book a session to get started!</p>}
        </EmptyState>
      ) : (
        Object.entries(groupedBookings).map(([date, dateBookings]) => (
          <DateGroup key={date}>
            <DateHeader>
              {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
            </DateHeader>
            {dateBookings.map((booking) => {
              const isPast = isBefore(parseISO(booking.session.start_time), new Date());
              const cancellationCheck = canCancelBooking(booking);
              const sessionStartTime = parseISO(booking.session.start_time);
              const now = new Date();
              const hoursUntilSession = differenceInHours(sessionStartTime, now);

              return (
                <BookingCard key={booking.id} $isPast={isPast}>
                  <TimeInfo>
                    <span>
                      {format(parseISO(booking.session.start_time), 'h:mm a')} - {format(parseISO(booking.session.end_time), 'h:mm a')}
                    </span>
                    <Badge $type={isPast ? 'past' : 'upcoming'}>
                      {isPast ? 'Past Session' : 'Upcoming'}
                    </Badge>
                  </TimeInfo>
                  <Title>{booking.session.title}</Title>
                  <Info>Location: {booking.session.school}</Info>
                  <Info>
                    Coach: {booking.session.coach
                      ? `${booking.session.coach.first_name} ${booking.session.coach.last_name}`
                      : 'No coach assigned'}
                  </Info>
                  {!isPast && hoursUntilSession <= 1 && (
                    <Info style={{
                      fontSize: theme.fontSizes.sm,
                      color: theme.colors.primary,
                      fontWeight: theme.fontWeights.medium,
                      display: 'flex',
                      alignItems: 'center',
                      gap: theme.spacing.sm
                    }}>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ width: '16px', height: '16px' }}>
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                      </svg>
                      You're all set! See you there
                    </Info>
                  )}
                  {!isPast && (
                    <CancelButton
                      onClick={() => handleCancelClick(booking)}
                      disabled={!cancellationCheck.canCancel || deleteBookingMutation.isPending}
                      title={!cancellationCheck.canCancel ? cancellationCheck.reason : ''}
                    >
                      {deleteBookingMutation.isPending && selectedBooking?.id === booking.id
                        ? 'Cancelling...'
                        : !cancellationCheck.canCancel
                          ? 'Cannot Cancel'
                          : 'Cancel Booking'
                      }
                    </CancelButton>
                  )}
                </BookingCard>
              );
            })}
          </DateGroup>
        ))
      )}
      <Modal
        isOpen={showCancelModal}
        onClose={() => {
          setShowCancelModal(false);
          setSelectedBooking(null);
        }}
        title="Cancel Booking"
        onConfirm={handleCancelConfirm}
        confirmText="Yes, Cancel"
        cancelText="No, Keep"
        loading={isCancelling || deleteBookingMutation.isPending}
      >
        {selectedBooking?.session && (
          <div>
            <p>Are you sure you want to cancel this booking?</p>
            <p><strong>{selectedBooking.session.title}</strong></p>
            <p>Date: {format(parseISO(selectedBooking.session.start_time), 'PPP')}</p>
            <p>Time: {format(parseISO(selectedBooking.session.start_time), 'h:mm a')}</p>
          </div>
        )}
      </Modal>
    </BookingsContainer>
  );
};

export default MyBookings;