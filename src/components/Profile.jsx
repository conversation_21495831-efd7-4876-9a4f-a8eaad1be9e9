import { useRef, useState } from 'react';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, FiEdit2, FiX } from 'react-icons/fi';
import styled from 'styled-components';
import { useAuth } from '../hooks/useAuth';
import { theme } from '../styles/theme';
import Spinner from './Spinner';

const ProfileContainer = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  border-radius: ${theme.borderRadius.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: ${theme.spacing.xl};
  margin: ${theme.spacing.lg} 0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.xl};

  @media (min-width: ${theme.breakpoints.md}) {
    flex-direction: row;
    align-items: center;
    text-align: left;
  }
`;

const ProfilePictureContainer = styled.div`
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: visible;
  margin: 0 auto;
  align-self: center;
  align-content: center;
  justify-content: center;
  align-items: center;
  display: flex;

  @media (min-width: ${theme.breakpoints.md}) {
    margin: 0;
  }
  `;

const ProfilePicture = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: ${props => props.$imageUrl
    ? `url(${props.$imageUrl})`
    : `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  cursor: pointer;
  position: relative;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border: 4px solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  }
`;

const EditOverlay = styled.div`
  width: 120px;
  height: 120px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: ${props => props.$isEditing ? 1 : 0};
  pointer-events: ${props => props.$isEditing ? 'auto' : 'none'};
  transition: all 0.3s ease;
  border-radius: 50%;
  color: white;
  gap: ${theme.spacing.xs};
  cursor: pointer;
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.medium};

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }
`;

const HiddenFileInput = styled.input`
  display: none;
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
`;

const ProfileInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.sm};
  width: 100%;

  @media (min-width: ${theme.breakpoints.md}) {
    width: auto;
  }
`;

const EditableField = styled.div`
  position: relative;
  display: inline-block;
  margin: 0 auto;
  
  @media (min-width: ${theme.breakpoints.md}) {
    margin: 0 ${theme.spacing.xl} 0 0;
  }
`;

const Name = styled.h2`
  margin: 0;
  color: ${theme.colors.text};
  font-size: ${theme.fontSizes.xl};
  ${props => props.isEditing && `
    background: ${theme.colors.background};
    border: 1px solid ${theme.colors.border};
    border-radius: ${theme.borderRadius.md};
    padding: ${theme.spacing.xs} ${theme.spacing.sm};
  `}
`;

const EditButton = styled.button`
  position: absolute;
  top: 50%;
  right: -${theme.spacing.xl};
  transform: translateY(-50%);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border: 1px solid rgba(255, 255, 255, 0.8);
  color: ${theme.colors.primary};
  cursor: pointer;
  padding: ${theme.spacing.xs};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const SaveCancelButtons = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
  margin-top: ${theme.spacing.md};
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border: none;
  border-radius: ${theme.borderRadius.md};
  background: ${props => props.$primary
    ? `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`
    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7))'};
  color: ${props => props.$primary ? 'white' : theme.colors.text};
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: ${theme.fontWeights.semibold};
  font-size: ${theme.fontSizes.sm};
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: ${props => props.$primary ? 'none' : '1px solid rgba(255, 255, 255, 0.8)'};
  backdrop-filter: ${props => props.$primary ? 'none' : 'blur(10px)'};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    &::before {
      display: none;
    }
  }
`;

const MembershipBadge = styled.span`
  display: inline-block;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  background: linear-gradient(135deg, ${theme.colors.success}, #059669);
  color: white;
  border-radius: ${theme.borderRadius.full};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.bold};
  margin-top: ${theme.spacing.sm};
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
`;

const Section = styled.div`
  margin-top: ${theme.spacing.xl};
`;

const SectionTitle = styled.h3`
  color: ${theme.colors.primary};
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing.lg};
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-bottom: ${theme.spacing.sm};

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary});
    border-radius: ${theme.borderRadius.sm};
  }
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${theme.spacing.lg};

  @media (max-width: ${theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
    gap: ${theme.spacing.md};
  }
`;

const DetailItem = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  padding: ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.lg};
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  ${props => props.$isEditing && `
    border-color: ${theme.colors.primary};
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  `}
`;

const DetailLabel = styled.div`
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.primary};
  margin-bottom: ${theme.spacing.sm};
  font-weight: ${theme.fontWeights.bold};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const DetailValue = styled.div`
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  font-weight: ${theme.fontWeights.medium};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: ${theme.spacing.sm};
  background: rgba(248, 250, 252, 0.5);
  border-radius: ${theme.borderRadius.sm};
  border: 1px solid rgba(0, 0, 0, 0.05);
`;

const Input = styled.input`
  width: 100%;
  padding: ${theme.spacing.md} ${theme.spacing.sm};
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.95);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${theme.spacing.md} ${theme.spacing.sm};
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.fontSizes.md};
  color: ${theme.colors.text};
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.95);
  }
`;

const Profile = () => {
  const { user, updateProfile } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    full_name: user?.user_metadata?.full_name || '',
    phone: user?.user_metadata?.phone || '',
    age: user?.user_metadata?.age || '',
    grade: user?.user_metadata?.grade || '',
    shirt_size: user?.user_metadata?.shirt_size || '',
    preferred_location: user?.user_metadata?.preferred_location || '',
    profile_picture: user?.user_metadata?.profile_picture || ''
  });
  
  const fileInputRef = useRef(null);

  if (!user) {
    return (
      <ProfileContainer>
        <div>Loading profile...</div>
      </ProfileContainer>
    );
  }

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form data if canceling
      setFormData({
        full_name: user?.user_metadata?.full_name || '',
        phone: user?.user_metadata?.phone || '',
        age: user?.user_metadata?.age || '',
        grade: user?.user_metadata?.grade || '',
        shirt_size: user?.user_metadata?.shirt_size || '',
        preferred_location: user?.user_metadata?.preferred_location || '',
        profile_picture: user?.user_metadata?.profile_picture || ''
      });
    }
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }
    
    // Validate file size (max 2MB for base64)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }
    
    try {
      setLoading(true);
      
      // Convert to base64
      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
      
      // Update user metadata with base64 image
      await updateProfile({
        ...user.user_metadata,
        profile_picture: base64
      });
      
      setFormData(prev => ({
        ...prev,
        profile_picture: base64
      }));
      
      toast.success('Profile picture updated successfully');
    } catch (error) {
      console.error('Error updating profile picture:', error);
      toast.error(error.message || 'Failed to update profile picture');
    } finally {
      setLoading(false);
    }
  };

  const handleProfilePictureClick = () => {
    if (isEditing) {
      fileInputRef.current?.click();
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      
      // Basic validation
      if (!formData.full_name.trim()) {
        toast.error('Name is required');
        return;
      }

      if (formData.phone && !/^\+?[\d\s-]+$/.test(formData.phone)) {
        toast.error('Invalid phone number');
        return;
      }

      const { error } = await updateProfile({
        ...user.user_metadata,
        ...formData
      });

      if (error) throw error;
      
      toast.success('Profile updated successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  // Function to get initials from name
  const getInitials = (name) => {
    return name
      ?.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase() || '?';
  };

  const renderValue = (key, value) => {
    if (!isEditing) {
      return <DetailValue>{value || 'Not provided'}</DetailValue>;
    }

    switch (key) {
      case 'shirt_size':
        return (
          <Select name={key} value={value} onChange={handleInputChange}>
            <option value="">Select size</option>
            <option value="XS">XS</option>
            <option value="S">S</option>
            <option value="M">M</option>
            <option value="L">L</option>
            <option value="XL">XL</option>
            <option value="2XL">2XL</option>
          </Select>
        );
      case 'preferred_location':
        return (
          <Select name={key} value={value} onChange={handleInputChange}>
          <option value="">Select School/Club</option>
          <option value="Curro Sitari">Curro Sitari</option>
          <option value="Generation Somerset West">Generation Somerset West</option>
          <option value="Hendrik Louw Primary">Hendrik Louw Primary</option>
          <option value="Lochnerhof Primary">Lochnerhof Primary</option>
          <option value="Somerset College">Somerset College</option>
          <option value="SWTC">SWTC</option>
          </Select>
        );
      case 'grade':
        return (
          <Select name={key} value={value} onChange={handleInputChange}>
            <option value="">Select experience</option>
            <option value="Beginner">Beginner</option>
            <option value="Intermediate">Intermediate</option>
            <option value="Advanced">Advanced</option>
          </Select>
        );
      default:
        return (
          <Input
            type={key === 'age' ? 'number' : 'text'}
            name={key}
            value={value}
            onChange={handleInputChange}
            placeholder={`Enter ${key.replace('_', ' ')}`}
          />
        );
    }
  };

  return (
    <ProfileContainer>
      <ProfileHeader>
        <ProfilePictureContainer>
          <ProfilePicture
            $imageUrl={formData.profile_picture}
            onClick={handleProfilePictureClick}
          >
            {!formData.profile_picture && getInitials(formData.full_name)}
          </ProfilePicture>
          {isEditing && (
            <>
              <EditOverlay 
                $isEditing={isEditing}
                onClick={handleProfilePictureClick}
              >
                <FiCamera />
                <span>Change Photo</span>
              </EditOverlay>
            </>
          )}
          <HiddenFileInput
            type="file"
            ref={fileInputRef}
            accept="image/*"
            onChange={handleFileSelect}
          />
        </ProfilePictureContainer>
        <ProfileInfo>
          <EditableField>
            {isEditing ? (
              <Input
                type="text"
                name="full_name"
                value={formData.full_name}
                onChange={handleInputChange}
                placeholder="Enter your name"
              />
            ) : (
              <Name>{formData.full_name.charAt(0).toUpperCase() + formData.full_name.slice(1) || user?.email}</Name>
            )}
            <EditButton onClick={handleEditToggle}>
              {isEditing ? <FiX /> : <FiEdit2 />}
            </EditButton>
          </EditableField>
          <div>
          <MembershipBadge>Active Member</MembershipBadge>
          {isEditing && (
            <SaveCancelButtons>
              <Button $primary onClick={handleSave} disabled={loading}>
                {loading ? <Spinner size="small" /> : <FiCheck />}
                Save Changes
              </Button>
              <Button onClick={handleEditToggle} disabled={loading}>
                <FiX />
                Cancel
              </Button>
            </SaveCancelButtons>
          )}
          </div>
        </ProfileInfo>
      </ProfileHeader>

      <Section>
        <SectionTitle>
          Personal Information
        </SectionTitle>
        <DetailGrid>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>Email</DetailLabel>
            <DetailValue>{user?.email}</DetailValue>
          </DetailItem>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>Phone</DetailLabel>
            {renderValue('phone', formData.phone)}
          </DetailItem>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>Age</DetailLabel>
            {renderValue('age', formData.age)}
          </DetailItem>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>experience</DetailLabel>
            {renderValue('grade', formData.grade)}
          </DetailItem>
        </DetailGrid>
      </Section>

      <Section>
        <SectionTitle>
          Preferences
        </SectionTitle>
        <DetailGrid>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>Shirt Size</DetailLabel>
            {renderValue('shirt_size', formData.shirt_size)}
          </DetailItem>
          <DetailItem $isEditing={isEditing}>
            <DetailLabel>Preferred Location</DetailLabel>
            {renderValue('preferred_location', formData.preferred_location)}
          </DetailItem>
        </DetailGrid>
      </Section>

      
    </ProfileContainer>
  );
};

export default Profile;
