import { useBooking } from '../hooks/useBookings';
import PropTypes from 'prop-types';

export const BookingsList = ({ sessionId }) => {
  const { data: bookings, isLoading } = useBooking(sessionId);

  if (isLoading) return <div>Loading bookings...</div>;

  return (
    <div className="bookings-list">
      <h3>Class Bookings</h3>

      {!bookings || bookings.length === 0 ? (
        <p>No bookings yet</p>
      ) : (
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Participants</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {bookings.map(booking => (
              <tr key={booking.id}>
                <td>
                  {booking.user_first_name} {booking.user_last_name}
                </td>
                <td>{booking.user_email}</td>
                <td>{booking.participant_count}</td>
                <td>{booking.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

BookingsList.propTypes = {
  sessionId: PropTypes.string.isRequired
};