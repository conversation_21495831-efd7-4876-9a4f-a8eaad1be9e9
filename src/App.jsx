import { useEffect } from 'react'; // Add this import
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { AdminProvider } from './context/AdminContext';
import { MembershipProvider } from './context/MembershipContext';
import { BookingProvider } from './context/BookingContext';
import ProtectedRoute from './components/ProtectedRoute';
import Navigation from './components/Navigation';
import AuthForm from './components/auth/AuthForm';
import SignupStepper from './components/auth/SignupStepper';
import UserDashboard from './pages/UserDashboard';
import AdminDashboard from './pages/AdminDashboard';
import AdminManagementBoard from './pages/AdminManagementBoard';
import Booking from './pages/Booking';
import Disclaimer from './pages/Disclaimer';
import PaddlePage from './pages/PaddlePage';
import Support from './pages/Support';
import { Toaster } from 'react-hot-toast';
import { supabase } from './lib/supabase';

function App() {
  useEffect(() => {
    const testSupabase = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Supabase auth error:', error.message);
        }
      } catch (err) {
        console.error('Error testing Supabase:', err);
      }
    };

    testSupabase();
  }, []);

  return (
    <Router>
      <AuthProvider>
        <AdminProvider>
          <MembershipProvider>
            <BookingProvider>
              <Navigation />
              <Toaster position="top-center" />
              <Routes>
                <Route path="/login" element={<AuthForm mode="login" />} />
                <Route path="/signup" element={<SignupStepper />} />
                <Route path="/support" element={<Support />} />
                <Route 
                  path="/dashboard" 
                  element={
                    <ProtectedRoute>
                      <UserDashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/admin/manage" 
                  element={
                    <ProtectedRoute>
                      <AdminManagementBoard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/admin/*" 
                  element={
                    <ProtectedRoute>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/booking" 
                  element={
                    <ProtectedRoute>
                      <Booking />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/disclaimer" 
                  element={
                    <ProtectedRoute>
                      <Disclaimer />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/paddle" 
                  element={
                    <ProtectedRoute>
                      <PaddlePage />
                    </ProtectedRoute>
                  } 
                />
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </BookingProvider>
          </MembershipProvider>
        </AdminProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
