import { parseISO, differenceInHours, isBefore } from 'date-fns';

/**
 * Check if a booking can be cancelled based on the 1-hour rule
 * @param {Object} booking - The booking object with session information
 * @returns {Object} - { canCancel: boolean, reason: string }
 */
export const canCancelBooking = (booking) => {
  if (!booking) {
    return { canCancel: false, reason: 'Booking not found' };
  }

  if (booking.status === 'cancelled') {
    return { canCancel: false, reason: 'Booking is already cancelled' };
  }

  if (!booking.session || !booking.session.start_time) {
    return { canCancel: false, reason: 'Session information not available' };
  }

  const sessionStartTime = parseISO(booking.session.start_time);
  const now = new Date();

  // Check if session has already started or passed
  if (isBefore(sessionStartTime, now)) {
    return { canCancel: false, reason: 'Session has already started or passed' };
  }

  // Check if within 1 hour of session start
  const hoursUntilSession = differenceInHours(sessionStartTime, now);
  
  if (hoursUntilSession < 1) {
    return { 
      canCancel: false, 
      reason: 'Cannot cancel less than 1 hour before session starts' 
    };
  }

  return { canCancel: true, reason: '' };
};

/**
 * Get a positive commitment message based on session timing
 * @param {Object} booking - The booking object with session information
 * @returns {Object} - { message: string, color: string, icon: string } or null
 */
export const getCommitmentMessage = (booking) => {
  if (!booking?.session?.start_time) {
    return null;
  }

  const sessionStartTime = parseISO(booking.session.start_time);
  const now = new Date();
  const hoursUntilSession = differenceInHours(sessionStartTime, now);

  // Only show commitment message for sessions starting within 1 hour
  if (hoursUntilSession <= 1) {
    return {
      message: "You're all set! See you there",
      color: 'primary',
      icon: 'check-circle'
    };
  }

  return null;
};
