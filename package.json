{"name": "helderbergpickleball", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:all": "concurrently \"bun dev\" \"cd backend && bun dev\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "setup-admin": "bun run scripts/setup-admin.js", "db:local:start": "./scripts/setup_local_dev.sh", "db:local:stop": "docker-compose down", "db:local:reset": "cat supabase/migrations/FULL_RESET.sql | docker-compose exec -T db psql -U postgres postgres", "db:backup": "./scripts/backup_database.sh", "db:restore": "./scripts/restore_database.sh", "db:prod:push": "supabase db push", "db:prod:backup:local": "./scripts/backup_prod_to_local.sh"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/material": "^7.0.1", "@supabase/supabase-js": "^2.47.10", "@tanstack/react-query": "^5.71.10", "@tanstack/react-query-devtools": "^5.71.10", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "md5": "^2.3.0", "react": "^18.3.1", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-router-dom": "^6.21.1", "styled-components": "^6.1.6"}, "devDependencies": {"@eslint/js": "^9.17.0", "@rollup/rollup-linux-x64-gnu": "^4.13.0", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.0", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "vite": "^6.0.3"}}