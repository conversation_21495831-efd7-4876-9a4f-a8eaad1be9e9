{"name": "helderbergpickleball-backend", "version": "1.0.0", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@supabase/supabase-js": "^2.39.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2"}}